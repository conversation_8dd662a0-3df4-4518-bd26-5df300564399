/* 排行榜页面样式 */
.container {
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
  min-height: 100vh;
  color: #fff;
}

/* 页面头部 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient 3s ease infinite;
  margin-bottom: 30rpx;
}

/* 标签页 */
.tabs {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.tab-btn {
  padding: 20rpx 50rpx;
  border-radius: 25rpx;
  font-size: 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #ccc;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  border-color: #00f2fe;
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 10rpx 30rpx rgba(79, 172, 254, 0.4);
}

/* 页面内容 */
.content {
  padding: 40rpx 30rpx 120rpx;
}

/* 个人统计卡片 */
.personal-stats {
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.15);
  margin-bottom: 40rpx;
  overflow: hidden;
  position: relative;
}

.personal-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 300%;
  animation: gradient 2s ease infinite;
}

.stats-header {
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.stats-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.stats-content {
  padding: 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  display: block;
}

.best-score {
  color: #00ff88;
  text-shadow: 0 0 15rpx #00ff88;
}

.rank {
  color: #ffd700;
  text-shadow: 0 0 15rpx #ffd700;
}

/* 排行榜容器 */
.leaderboard-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20rpx;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.leaderboard-header {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leaderboard-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

/* 筛选选项 */
.filter-options {
  display: flex;
  gap: 15rpx;
}

.filter-btn {
  padding: 10rpx 20rpx;
  border-radius: 15rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ccc;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-color: #667eea;
  color: #fff;
}

.invite-btn {
  font-size: 24rpx;
  padding: 15rpx 30rpx;
}

/* 排行榜列表 */
.leaderboard-list,
.friends-list {
  padding: 20rpx;
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.rank-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.rank-item:active::before {
  left: 100%;
}

.rank-item.top-three {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 140, 0, 0.1));
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 10rpx 30rpx rgba(255, 215, 0, 0.2);
}

.rank-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.1);
}

/* 排名号码 */
.rank-number {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.rank-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}

.crown {
  font-size: 40rpx;
}

.medal {
  font-size: 35rpx;
}

/* 头像 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

/* 用户信息 */
.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.achievement {
  font-size: 22rpx;
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  align-self: flex-start;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.status {
  font-size: 24rpx;
  color: #999;
}

/* 分数信息 */
.score-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.score {
  font-size: 36rpx;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 10rpx #00ff88;
}

.time {
  font-size: 22rpx;
  color: #999;
}

.challenge-btn {
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 15rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx;
  gap: 30rpx;
}

.loading-text {
  color: #667eea;
  font-size: 28rpx;
}

/* 无好友提示 */
.empty-friends {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 50rpx;
  display: block;
  line-height: 1.5;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.bottom-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 25rpx;
  font-weight: bold;
}

/* 用户详情弹窗 */
.user-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 30rpx;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 详情头部 */
.detail-header {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.detail-info {
  flex: 1;
}

.detail-nickname {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 10rpx;
}

.detail-rank {
  font-size: 28rpx;
  color: #ffd700;
}

/* 详情统计 */
.detail-stats {
  padding: 40rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.detail-stat {
  text-align: center;
}

.detail-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.detail-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  display: block;
}

/* 成就 */
.detail-achievements {
  padding: 0 40rpx 40rpx;
}

.achievements-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  display: block;
}

.achievements-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 15rpx;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 15rpx;
}

.achievement-icon {
  font-size: 24rpx;
}

.achievement-name {
  font-size: 22rpx;
  color: #ffd700;
}

/* 详情操作 */
.detail-actions {
  padding: 40rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 25rpx;
}

/* 动画效果 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .content {
    padding: 30rpx 20rpx 120rpx;
  }
  
  .stats-content {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .tabs {
    gap: 20rpx;
  }
  
  .tab-btn {
    padding: 15rpx 35rpx;
    font-size: 28rpx;
  }
  
  .rank-item {
    padding: 20rpx;
    margin-bottom: 15rpx;
  }
  
  .nickname {
    font-size: 28rpx;
  }
  
  .score {
    font-size: 32rpx;
  }
  
  .detail-header {
    padding: 30rpx;
  }
  
  .detail-avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .detail-stats {
    padding: 30rpx;
    gap: 20rpx;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 15rpx;
  }
} 
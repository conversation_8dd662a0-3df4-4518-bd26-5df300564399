<view class="simple-game-container">
  <!-- 标题 -->
  <view class="game-header">
    <text class="game-title">3D贪吃蛇 - 简化测试版</text>
    <text class="status-message">{{testMessage}}</text>
  </view>

  <!-- 游戏信息 -->
  <view class="game-info">
    <view class="info-item">难度: {{difficulty}}</view>
    <view class="info-item">状态: {{gameState}}</view>
    <view class="info-item">分数: {{score}}</view>
    <view class="info-item">长度: {{snakeLength}}</view>
  </view>

  <!-- 测试Canvas -->
  <view class="canvas-section">
    <text class="section-title">Canvas测试</text>
    <canvas 
      class="game-canvas" 
      type="webgl"
      id="gameCanvas"
      style="width: 300px; height: 200px; border: 2px solid #007aff;">
    </canvas>
    <button class="test-btn" bindtap="testCanvas">测试Canvas</button>
    <button class="test-btn" bindtap="testGameEngine">测试GameEngine</button>
  </view>

  <!-- 游戏控制 -->
  <view class="control-section">
    <text class="section-title">游戏控制</text>
    
    <view class="control-buttons">
      <button 
        class="control-btn start-btn" 
        bindtap="startGame"
        wx:if="{{gameState === 'ready'}}">
        开始游戏
      </button>
      
      <button 
        class="control-btn pause-btn" 
        bindtap="pauseGame"
        wx:if="{{gameState === 'playing'}}">
        暂停游戏
      </button>
      
      <button 
        class="control-btn resume-btn" 
        bindtap="resumeGame"
        wx:if="{{gameState === 'paused'}}">
        继续游戏
      </button>
      
      <button 
        class="control-btn restart-btn" 
        bindtap="restartGame">
        重新开始
      </button>
      
      <button 
        class="control-btn exit-btn" 
        bindtap="exitGame">
        退出游戏
      </button>
    </view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在初始化游戏...</text>
  </view>
</view> 
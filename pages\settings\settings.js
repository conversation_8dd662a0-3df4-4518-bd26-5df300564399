const app = getApp()

Page({
  data: {
    settings: {
      soundEnabled: true,
      volume: 80,
      vibrationEnabled: true,
      vibrationIntensity: 'medium',
      controlMode: 'joystick',
      sensitivity: 5,
      showFPS: false,
      graphicsQuality: 'medium',
      defaultDifficulty: 'normal',
      autoPause: true
    },
    bestScore: 0,
    gameCount: 0
  },

  onLoad() {
    this.loadSettings()
  },

  onShow() {
    // 刷新数据
    this.loadSettings()
  },

  // 加载设置
  loadSettings() {
    const globalData = app.globalData
    
    this.setData({
      settings: {
        ...this.data.settings,
        ...globalData.gameSettings
      },
      bestScore: globalData.bestScore,
      gameCount: this.getGameCount()
    })
  },

  // 获取游戏次数
  getGameCount() {
    try {
      return wx.getStorageSync('gameCount') || 0
    } catch (error) {
      return 0
    }
  },

  // 音效开关变化
  onSoundChange(e) {
    const soundEnabled = e.detail.value
    this.setData({
      'settings.soundEnabled': soundEnabled
    })
    
    // 实时预览
    if (soundEnabled) {
      this.playTestSound()
    }
  },

  // 音量变化
  onVolumeChange(e) {
    const volume = e.detail.value
    this.setData({
      'settings.volume': volume
    })
    
    // 实时预览
    if (this.data.settings.soundEnabled) {
      this.playTestSound()
    }
  },

  // 震动开关变化
  onVibrationChange(e) {
    const vibrationEnabled = e.detail.value
    this.setData({
      'settings.vibrationEnabled': vibrationEnabled
    })
    
    // 实时预览
    if (vibrationEnabled) {
      this.testVibration()
    }
  },

  // 选择震动强度
  selectVibrationIntensity(e) {
    const intensity = e.currentTarget.dataset.intensity
    this.setData({
      'settings.vibrationIntensity': intensity
    })
    
    // 实时预览
    this.testVibration(intensity)
  },

  // 选择控制模式
  selectControlMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      'settings.controlMode': mode
    })
    
    wx.vibrateShort()
    
    // 如果选择重力感应，检查权限
    if (mode === 'gyroscope') {
      this.checkGyroscopePermission()
    }
  },

  // 检查重力感应权限
  checkGyroscopePermission() {
    wx.startGyroscope({
      interval: 'game',
      success: () => {
        wx.stopGyroscope()
        wx.showToast({
          title: '重力感应已启用',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showModal({
          title: '权限不足',
          content: '使用重力感应需要授权访问设备传感器',
          showCancel: false
        })
        
        // 回退到摇杆模式
        this.setData({
          'settings.controlMode': 'joystick'
        })
      }
    })
  },

  // 灵敏度变化
  onSensitivityChange(e) {
    const sensitivity = e.detail.value
    this.setData({
      'settings.sensitivity': sensitivity
    })
  },

  // 显示FPS开关变化
  onShowFPSChange(e) {
    const showFPS = e.detail.value
    this.setData({
      'settings.showFPS': showFPS
    })
  },

  // 选择画质
  selectGraphicsQuality(e) {
    const quality = e.currentTarget.dataset.quality
    this.setData({
      'settings.graphicsQuality': quality
    })
    
    wx.vibrateShort()
    
    // 显示画质说明
    const qualityDesc = {
      low: '省电模式：降低渲染质量，延长电池续航',
      medium: '平衡模式：兼顾画质和性能的最佳选择',
      high: '高画质模式：最佳视觉效果，消耗更多电量'
    }
    
    wx.showToast({
      title: qualityDesc[quality],
      icon: 'none',
      duration: 2000
    })
  },

  // 选择默认难度
  selectDefaultDifficulty(e) {
    const difficulty = e.currentTarget.dataset.difficulty
    this.setData({
      'settings.defaultDifficulty': difficulty
    })
    
    wx.vibrateShort()
  },

  // 自动暂停开关变化
  onAutoPauseChange(e) {
    const autoPause = e.detail.value
    this.setData({
      'settings.autoPause': autoPause
    })
  },

  // 导出数据
  exportData() {
    try {
      const gameData = {
        bestScore: this.data.bestScore,
        gameCount: this.data.gameCount,
        settings: this.data.settings,
        exportTime: new Date().toISOString()
      }
      
      const dataString = JSON.stringify(gameData, null, 2)
      
      // 复制到剪贴板
      wx.setClipboardData({
        data: dataString,
        success: () => {
          wx.showModal({
            title: '导出成功',
            content: '游戏数据已复制到剪贴板，您可以将其保存到安全的地方',
            showCancel: false
          })
        }
      })
    } catch (error) {
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      })
    }
  },

  // 重置数据
  resetData() {
    wx.showModal({
      title: '确认重置',
      content: '这将清除所有游戏数据，包括最高分和游戏记录，是否确认？',
      confirmText: '确认重置',
      confirmColor: '#ff4b2b',
      success: (res) => {
        if (res.confirm) {
          this.performDataReset()
        }
      }
    })
  },

  // 执行数据重置
  performDataReset() {
    try {
      // 清除本地存储
      wx.removeStorageSync('bestScore')
      wx.removeStorageSync('gameCount')
      wx.removeStorageSync('gameSettings')
      
      // 重置全局数据
      app.globalData.bestScore = 0
      app.globalData.currentScore = 0
      
      // 重置界面显示
      this.setData({
        bestScore: 0,
        gameCount: 0
      })
      
      wx.showToast({
        title: '重置成功',
        icon: 'success'
      })
      
      // 震动反馈
      wx.vibrateLong()
      
    } catch (error) {
      wx.showToast({
        title: '重置失败',
        icon: 'error'
      })
    }
  },

  // 显示帮助
  showHelp() {
    wx.showModal({
      title: '游戏帮助',
      content: `游戏玩法：
• 控制3D蛇身移动吃食物
• 避免撞墙和撞到自己
• 支持多种控制方式
• 不同难度有不同障碍物

控制方式：
• 虚拟摇杆：拖拽控制方向
• 方向按钮：点击控制方向  
• 重力感应：倾斜手机控制

祝您游戏愉快！`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 联系我们
  contactUs() {
    wx.showModal({
      title: '联系我们',
      content: '如果您在游戏中遇到问题或有改进建议，请通过以下方式联系我们：\n\n微信客服：game_support\n邮箱：<EMAIL>\n\n感谢您的支持！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 恢复默认设置
  resetToDefault() {
    wx.showModal({
      title: '恢复默认',
      content: '是否将所有设置恢复为默认值？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            settings: {
              soundEnabled: true,
              volume: 80,
              vibrationEnabled: true,
              vibrationIntensity: 'medium',
              controlMode: 'joystick',
              sensitivity: 5,
              showFPS: false,
              graphicsQuality: 'medium',
              defaultDifficulty: 'normal',
              autoPause: true
            }
          })
          
          wx.showToast({
            title: '已恢复默认',
            icon: 'success'
          })
        }
      }
    })
  },

  // 保存并返回
  saveAndExit() {
    this.saveSettings()
    wx.navigateBack()
  },

  // 保存设置
  saveSettings() {
    try {
      // 更新全局数据
      Object.assign(app.globalData.gameSettings, this.data.settings)
      
      // 保存到本地存储
      app.saveGameSettings()
      
      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })
      
    } catch (error) {
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  // 播放测试音效
  playTestSound() {
    if (!this.data.settings.soundEnabled) return
    
    const audio = wx.createInnerAudioContext()
    audio.src = '/assets/sounds/button.mp3'
    audio.volume = this.data.settings.volume / 100
    audio.autoplay = true
    audio.onError(() => {
      console.warn('测试音效播放失败')
    })
  },

  // 测试震动
  testVibration(intensity) {
    if (!this.data.settings.vibrationEnabled) return
    
    const currentIntensity = intensity || this.data.settings.vibrationIntensity
    
    switch (currentIntensity) {
      case 'light':
        wx.vibrateShort({ type: 'light' })
        break
      case 'medium':
        wx.vibrateShort({ type: 'medium' })
        break
      case 'heavy':
        wx.vibrateShort({ type: 'heavy' })
        break
    }
  },

  // 页面卸载时保存设置
  onUnload() {
    this.saveSettings()
  }
}) 
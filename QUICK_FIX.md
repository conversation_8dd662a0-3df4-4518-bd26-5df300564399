# 真机调试黑屏问题 - 快速修复指南

## 🚨 问题现象
- 真机调试时出现黑屏
- 控制台显示页面注册错误
- Component路径错误

## ✅ 已实施的修复

### 1. 修复了app.js语法错误
- 移除了文件末尾的多余空格和分号
- 确保JavaScript语法正确

### 2. 创建了调试版本
- `pages/index/index-debug.js` - 简化的调试页面
- `pages/index/index-debug.wxml` - 调试界面
- `pages/index/index-debug.wxss` - 调试样式

### 3. 临时设置调试页面为首页
- 修改了app.json，将调试页面设为第一个页面

## 🔧 测试步骤

### 1. 重新编译项目
```
1. 在微信开发者工具中点击"清缓存" → "清除全部缓存数据"
2. 等待项目重新编译
3. 重新进行真机调试
```

### 2. 使用调试版本测试
现在首页会显示调试界面，包含：
- 设备信息显示
- WebGL兼容性测试
- 用户登录测试
- 游戏功能测试

### 3. 检查调试信息
在调试页面中查看：
- 设备型号和微信版本
- WebGL支持情况
- 页面加载状态
- 错误信息

## 🎯 排查重点

### WebGL兼容性
1. 点击"测试WebGL支持"按钮
2. 查看是否显示WebGL版本信息
3. 如果不支持WebGL，说明设备不兼容3D功能

### 页面跳转测试
1. 点击"开始游戏"按钮
2. 查看是否能正常跳转到游戏页面
3. 观察控制台错误信息

### 设备性能检查
查看调试信息中的：
- 性能等级 (benchmarkLevel)
- 内存情况
- 微信版本是否过低

## 🔄 恢复正常版本

测试完成后，如需恢复正常版本：

1. 修改 `app.json`，将首页改回：
```json
"pages": [
  "pages/index/index",
  "pages/game/game",
  ...
]
```

2. 重新编译项目

## 📋 常见问题解决

### 问题1: 设备不支持WebGL
**解决方案：**
- 使用支持WebGL的设备
- 更新微信到最新版本
- 考虑降级为2D版本

### 问题2: 微信版本过低
**解决方案：**
- 更新微信到7.0.0以上版本
- 检查小程序基础库版本

### 问题3: 内存不足
**解决方案：**
- 关闭其他应用释放内存
- 降低游戏画质设置
- 使用性能更好的设备

## 📞 技术支持

如果调试版本仍然黑屏，请提供：
1. 调试页面显示的设备信息截图
2. 控制台完整错误日志
3. 设备型号和微信版本

---
**创建时间：** 2024年12月
**状态：** 调试版本已部署 ✅ 
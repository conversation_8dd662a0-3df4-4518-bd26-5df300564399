<view class="container">
  <view class="header">
    <text class="title">🏆 排行榜</text>
    <view class="tabs">
      <button 
        class="tab-btn {{currentTab === 'global' ? 'active' : ''}}"
        data-tab="global"
        bindtap="switchTab">
        全球排行
      </button>
      <button 
        class="tab-btn {{currentTab === 'friends' ? 'active' : ''}}"
        data-tab="friends"
        bindtap="switchTab">
        好友排行
      </button>
    </view>
  </view>
  
  <view class="content">
    <!-- 个人成绩卡片 -->
    <view class="card personal-stats">
      <view class="stats-header">
        <text class="stats-title">📊 个人成绩</text>
      </view>
      <view class="stats-content">
        <view class="stat-item">
          <text class="stat-label">最高分</text>
          <text class="stat-value best-score">{{personalStats.bestScore}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">全球排名</text>
          <text class="stat-value rank">第 {{personalStats.globalRank}} 名</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">游戏次数</text>
          <text class="stat-value">{{personalStats.gameCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">平均分</text>
          <text class="stat-value">{{personalStats.averageScore}}</text>
        </view>
      </view>
    </view>
    
    <!-- 全球排行榜 -->
    <view class="leaderboard-container" wx:if="{{currentTab === 'global'}}">
      <view class="leaderboard-header">
        <text class="leaderboard-title">🌟 全球高手榜</text>
        <view class="filter-options">
          <button 
            class="filter-btn {{timeFilter === 'all' ? 'active' : ''}}"
            data-filter="all"
            bindtap="changeTimeFilter">
            全部
          </button>
          <button 
            class="filter-btn {{timeFilter === 'week' ? 'active' : ''}}"
            data-filter="week"
            bindtap="changeTimeFilter">
            本周
          </button>
          <button 
            class="filter-btn {{timeFilter === 'month' ? 'active' : ''}}"
            data-filter="month"
            bindtap="changeTimeFilter">
            本月
          </button>
        </view>
      </view>
      
      <view class="leaderboard-list">
        <view 
          class="rank-item {{index < 3 ? 'top-three' : ''}}"
          wx:for="{{globalLeaderboard}}"
          wx:key="id"
          data-user="{{item}}"
          bindtap="showUserDetail">
          
          <view class="rank-number">
            <text class="rank-text" wx:if="{{index >= 3}}">{{index + 1}}</text>
            <text class="crown" wx:if="{{index === 0}}">👑</text>
            <text class="medal" wx:if="{{index === 1}}">🥈</text>
            <text class="medal" wx:if="{{index === 2}}">🥉</text>
          </view>
          
          <image class="avatar" src="{{item.avatarUrl}}" />
          
          <view class="user-info">
            <text class="nickname">{{item.nickname}}</text>
            <text class="achievement" wx:if="{{item.achievement}}">{{item.achievement}}</text>
          </view>
          
          <view class="score-info">
            <text class="score">{{item.score}}</text>
            <text class="time">{{item.timeAgo}}</text>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMore && !loading}}">
          <button class="btn btn-secondary" bindtap="loadMore">加载更多</button>
        </view>
        
        <!-- 加载中 -->
        <view class="loading-container" wx:if="{{loading}}">
          <view class="loading"></view>
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </view>
    
    <!-- 好友排行榜 -->
    <view class="leaderboard-container" wx:if="{{currentTab === 'friends'}}">
      <view class="leaderboard-header">
        <text class="leaderboard-title">👥 好友排行</text>
        <button class="btn btn-secondary invite-btn" bindtap="inviteFriends">
          邀请好友
        </button>
      </view>
      
      <view class="friends-list" wx:if="{{friendsLeaderboard.length > 0}}">
        <view 
          class="rank-item friend-item"
          wx:for="{{friendsLeaderboard}}"
          wx:key="id"
          data-user="{{item}}"
          bindtap="showUserDetail">
          
          <view class="rank-number">
            <text class="rank-text">{{index + 1}}</text>
          </view>
          
          <image class="avatar" src="{{item.avatarUrl}}" />
          
          <view class="user-info">
            <text class="nickname">{{item.nickname}}</text>
            <text class="status">{{item.isOnline ? '在线' : item.lastOnline}}</text>
          </view>
          
          <view class="score-info">
            <text class="score">{{item.score}}</text>
            <button 
              class="btn btn-primary challenge-btn"
              data-user="{{item}}"
              bindtap="challengeFriend">
              挑战
            </button>
          </view>
        </view>
      </view>
      
      <!-- 无好友提示 -->
      <view class="empty-friends" wx:else>
        <text class="empty-icon">👫</text>
        <text class="empty-title">还没有好友记录</text>
        <text class="empty-desc">邀请朋友一起游戏，比拼高分吧！</text>
        <button class="btn btn-success" bindtap="inviteFriends">
          邀请好友
        </button>
      </view>
    </view>
  </view>
  
  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="btn btn-secondary" bindtap="refreshData">
      🔄 刷新数据
    </button>
    <button class="btn btn-primary" bindtap="shareRank">
      📤 分享成绩
    </button>
  </view>
  
  <!-- 用户详情弹窗 -->
  <view class="user-detail-modal" wx:if="{{showUserDetail}}">
    <view class="modal-mask" bindtap="hideUserDetail"></view>
    <view class="modal-content">
      <view class="detail-header">
        <image class="detail-avatar" src="{{selectedUser.avatarUrl}}" />
        <view class="detail-info">
          <text class="detail-nickname">{{selectedUser.nickname}}</text>
          <text class="detail-rank">全球排名：第{{selectedUser.rank}}名</text>
        </view>
      </view>
      
      <view class="detail-stats">
        <view class="detail-stat">
          <text class="detail-label">最高分</text>
          <text class="detail-value">{{selectedUser.score}}</text>
        </view>
        <view class="detail-stat">
          <text class="detail-label">游戏次数</text>
          <text class="detail-value">{{selectedUser.gameCount}}</text>
        </view>
        <view class="detail-stat">
          <text class="detail-label">胜率</text>
          <text class="detail-value">{{selectedUser.winRate}}%</text>
        </view>
        <view class="detail-stat">
          <text class="detail-label">注册时间</text>
          <text class="detail-value">{{selectedUser.joinDate}}</text>
        </view>
      </view>
      
      <view class="detail-achievements" wx:if="{{selectedUser.achievements}}">
        <text class="achievements-title">🏅 成就</text>
        <view class="achievements-list">
          <view 
            class="achievement-item"
            wx:for="{{selectedUser.achievements}}"
            wx:key="id">
            <text class="achievement-icon">{{item.icon}}</text>
            <text class="achievement-name">{{item.name}}</text>
          </view>
        </view>
      </view>
      
      <view class="detail-actions">
        <button class="btn btn-secondary" bindtap="hideUserDetail">
          关闭
        </button>
        <button 
          class="btn btn-primary"
          wx:if="{{selectedUser.id !== userInfo.id}}"
          data-user="{{selectedUser}}"
          bindtap="challengeFriend">
          发起挑战
        </button>
      </view>
    </view>
  </view>
</view> 
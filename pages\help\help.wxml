<view class="container">
  <view class="header">
    <text class="title">🎮 游戏帮助</text>
  </view>
  
  <scroll-view class="content" scroll-y="true">
    <!-- 游戏介绍 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">🐍 游戏介绍</text>
      </view>
      <view class="section-content">
        <text class="desc">
          3D贪吃蛇是一款基于WebGL技术的立体贪吃蛇游戏。在三维空间中控制蛇身移动，吃食物变长，避免撞墙和自己的身体，挑战更高分数！
        </text>
      </view>
    </view>
    
    <!-- 游戏规则 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">📋 游戏规则</text>
      </view>
      <view class="section-content">
        <view class="rule-item">
          <text class="rule-icon">🎯</text>
          <text class="rule-text">控制蛇头移动，吃到红色食物可以增长身体</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">⚠️</text>
          <text class="rule-text">避免撞到墙壁、障碍物或自己的身体</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">🏆</text>
          <text class="rule-text">每吃一个食物得10分，身体越长分数越高</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">⭐</text>
          <text class="rule-text">挑战不同难度，获得更高分数和成就</text>
        </view>
      </view>
    </view>
    
    <!-- 控制方式 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">🎮 控制方式</text>
      </view>
      <view class="section-content">
        <view class="control-item">
          <view class="control-header">
            <text class="control-icon">🕹️</text>
            <text class="control-name">虚拟摇杆</text>
          </view>
          <text class="control-desc">拖拽屏幕左下角的圆形摇杆控制蛇的移动方向</text>
        </view>
        
        <view class="control-item">
          <view class="control-header">
            <text class="control-icon">⬆️</text>
            <text class="control-name">方向按钮</text>
          </view>
          <text class="control-desc">点击屏幕右下角的方向按钮（上下左右）控制移动</text>
        </view>
        
        <view class="control-item">
          <view class="control-header">
            <text class="control-icon">📱</text>
            <text class="control-name">重力感应</text>
          </view>
          <text class="control-desc">倾斜手机来控制蛇的移动方向（需要在设置中开启）</text>
        </view>
        
        <view class="control-item">
          <view class="control-header">
            <text class="control-icon">👆</text>
            <text class="control-name">滑动手势</text>
          </view>
          <text class="control-desc">在屏幕上滑动来快速改变移动方向</text>
        </view>
      </view>
    </view>
    
    <!-- 难度说明 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">🎯 难度等级</text>
      </view>
      <view class="section-content">
        <view class="difficulty-item easy">
          <view class="diff-header">
            <text class="diff-icon">🟢</text>
            <text class="diff-name">简单</text>
          </view>
          <text class="diff-desc">移动速度较慢，障碍物较少，适合新手练习</text>
        </view>
        
        <view class="difficulty-item normal">
          <view class="diff-header">
            <text class="diff-icon">🟡</text>
            <text class="diff-name">普通</text>
          </view>
          <text class="diff-desc">中等移动速度，适量障碍物，平衡的挑战体验</text>
        </view>
        
        <view class="difficulty-item hard">
          <view class="diff-header">
            <text class="diff-icon">🔴</text>
            <text class="diff-name">困难</text>
          </view>
          <text class="diff-desc">快速移动，大量障碍物，考验反应和技巧</text>
        </view>
      </view>
    </view>
    
    <!-- 游戏技巧 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">💡 游戏技巧</text>
      </view>
      <view class="section-content">
        <view class="tip-item">
          <text class="tip-number">1</text>
          <view class="tip-content">
            <text class="tip-title">提前规划路径</text>
            <text class="tip-desc">预测蛇身的移动轨迹，避免进入死角</text>
          </view>
        </view>
        
        <view class="tip-item">
          <text class="tip-number">2</text>
          <view class="tip-content">
            <text class="tip-title">靠边移动</text>
            <text class="tip-desc">沿着墙壁移动可以减少被自己身体困住的风险</text>
          </view>
        </view>
        
        <view class="tip-item">
          <text class="tip-number">3</text>
          <view class="tip-content">
            <text class="tip-title">观察全局</text>
            <text class="tip-desc">利用3D视角观察整个场景，找到最佳移动路线</text>
          </view>
        </view>
        
        <view class="tip-item">
          <text class="tip-number">4</text>
          <view class="tip-content">
            <text class="tip-title">控制节奏</text>
            <text class="tip-desc">不要急于求成，稳定的移动比快速冲刺更有效</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 特殊功能 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">✨ 特殊功能</text>
      </view>
      <view class="section-content">
        <view class="feature-item">
          <text class="feature-icon">💥</text>
          <view class="feature-content">
            <text class="feature-title">粒子特效</text>
            <text class="feature-desc">吃食物时会产生炫酷的粒子爆炸效果</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">📳</text>
          <view class="feature-content">
            <text class="feature-title">震动反馈</text>
            <text class="feature-desc">游戏事件会触发手机震动，增强沉浸感</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">🎵</text>
          <view class="feature-content">
            <text class="feature-title">3D音效</text>
            <text class="feature-desc">空间音效让您感受立体的游戏世界</text>
          </view>
        </view>
        
        <view class="feature-item">
          <text class="feature-icon">📊</text>
          <view class="feature-content">
            <text class="feature-title">实时FPS</text>
            <text class="feature-desc">可在设置中开启FPS显示，监控游戏性能</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 常见问题 -->
    <view class="card section">
      <view class="section-title">
        <text class="title-text">❓ 常见问题</text>
      </view>
      <view class="section-content">
        <view class="faq-item">
          <text class="faq-question">Q: 游戏卡顿怎么办？</text>
          <text class="faq-answer">A: 可以在设置中降低画质到"省电模式"，关闭一些特效来提升流畅度。</text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">Q: 重力感应不灵敏？</text>
          <text class="faq-answer">A: 请在设置中调整灵敏度，建议设置为5-7之间。确保手机传感器正常工作。</text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">Q: 如何保存游戏记录？</text>
          <text class="faq-answer">A: 请先进行微信登录，游戏数据会自动保存到本地和云端。</text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">Q: 支持哪些设备？</text>
          <text class="faq-answer">A: 支持iOS 10.0+和Android 6.0+，需要微信7.0.0+版本，设备需要支持WebGL。</text>
        </view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn btn-secondary" bindtap="backToGame">开始游戏</button>
    <button class="btn btn-primary" bindtap="goToSettings">游戏设置</button>
  </view>
</view> 
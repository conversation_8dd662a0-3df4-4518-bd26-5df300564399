const GameEngine = require('../../utils/GameEngine.js')
const app = getApp()

Page({
  data: {
    // 游戏状态
    gameState: 'loading', // loading, playing, paused, gameOver
    score: 0,
    snakeLength: 3,
    isNewRecord: false,
    isLoading: true,
    showFPS: false,
    fps: 60,
    
    // 控制设置
    controlMode: 'joystick', // joystick, buttons, gyroscope
    joystick: {
      x: 0,
      y: 0,
      active: false
    },
    
    // 难度设置
    difficulty: 'normal'
  },

  // 游戏引擎实例
  gameEngine: null,
  
  // 动画帧ID
  animationId: null,
  
  // 触摸相关
  touchStartPos: null,
  joystickCenter: null,
  
  // 重力感应
  gyroscopeListener: null,

  onLoad(options) {
    // 获取传入的难度参数
    if (options.difficulty) {
      this.setData({
        difficulty: options.difficulty
      })
    }
    
    // 初始化游戏
    this.initGame()
  },

  onShow() {
    // 页面显示时继续动画
    if (this.data.gameState === 'playing' && !this.animationId) {
      this.startGameLoop()
    }
    
    // 启用重力感应（如果设置了）
    this.enableGyroscope()
  },

  onHide() {
    // 页面隐藏时暂停游戏
    if (this.data.gameState === 'playing') {
      this.pauseGame()
    }
    
    // 停止动画循环
    this.stopGameLoop()
    
    // 禁用重力感应
    this.disableGyroscope()
  },

  onUnload() {
    // 清理资源
    this.cleanup()
  },

  // 初始化游戏
  async initGame() {
    try {
      this.setData({ isLoading: true })
      
      // 检查设备兼容性
      const systemInfo = wx.getSystemInfoSync()
      console.log('设备信息:', systemInfo)
      
      // 检查微信版本
      if (systemInfo.version < '7.0.0') {
        throw new Error('微信版本过低，请更新到7.0.0以上版本')
      }
      
      // 检查设备性能
      if (systemInfo.benchmarkLevel !== undefined && systemInfo.benchmarkLevel < 1) {
        console.warn('设备性能较低，可能影响游戏体验')
        wx.showToast({
          title: '设备性能较低，建议降低画质',
          icon: 'none',
          duration: 3000
        })
      }
      
      // 获取Canvas节点（添加重试机制）
      let retryCount = 0
      const maxRetries = 3
      
      const getCanvasNode = () => {
        const query = wx.createSelectorQuery()
        query.select('#gameCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            console.log('Canvas查询结果:', res)
            if (res && res[0] && res[0].node) {
              this.setupGameEngine(res[0])
            } else {
              retryCount++
              if (retryCount < maxRetries) {
                console.warn(`Canvas节点获取失败，重试第${retryCount}次...`)
                setTimeout(getCanvasNode, 200)
              } else {
                console.error('Canvas节点获取失败，已重试', maxRetries, '次，查询结果:', res)
                throw new Error('Canvas节点获取失败，请检查页面结构')
              }
            }
          })
      }
      
      // 延迟获取，确保DOM渲染完成
      setTimeout(getCanvasNode, 100)
      
    } catch (error) {
      console.error('游戏初始化失败:', error)
      this.setData({ isLoading: false })
      wx.showModal({
        title: '初始化失败',
        content: error.message,
        showCancel: false,
        confirmText: '返回首页',
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  // 设置游戏引擎
  async setupGameEngine(canvasInfo) {
    try {
      console.log('开始设置游戏引擎...')
      
      const canvas = canvasInfo.node
      const dpr = wx.getSystemInfoSync().pixelRatio
      
      console.log('Canvas信息:', {
        width: canvasInfo.width,
        height: canvasInfo.height,
        pixelRatio: dpr
      })
      
      // 设置Canvas尺寸
      canvas.width = canvasInfo.width * dpr
      canvas.height = canvasInfo.height * dpr
      
      // 保存Canvas引用，用于获取requestAnimationFrame
      this.canvas = canvas
      
      // 运行设备兼容性诊断
      console.log('运行设备兼容性诊断...')
      const diagnosis = GameEngine.diagnoseCompatibility(canvas)
      
      if (diagnosis.errors.length > 0) {
        throw new Error(diagnosis.errors.join('; '))
      }
      
      if (diagnosis.warnings.length > 0) {
        console.warn('兼容性警告:', diagnosis.warnings)
        // 显示警告但不阻止游戏体验
        wx.showToast({
          title: '设备兼容性警告，可能影响游戏体验',
          icon: 'none',
          duration: 2000
        })
      }
      
      console.log('创建GameEngine实例...')
      // 创建游戏引擎
      this.gameEngine = new GameEngine(canvas)
      this.gameEngine.setDifficulty(this.data.difficulty)
      
      console.log('等待引擎初始化...')
      // 等待引擎初始化完成
      await this.waitForEngineReady()
      
      console.log('启动游戏循环...')
      // 启动游戏循环
      this.startGameLoop()
      
      console.log('游戏引擎设置完成')
      
      // 更新界面状态
      this.setData({
        isLoading: false,
        gameState: 'ready'
      })
      
      console.log('游戏引擎设置完成!')
      
      // 自动开始游戏
      setTimeout(() => {
        this.startGame()
      }, 1000)
      
    } catch (error) {
      console.error('游戏引擎设置失败:', error)
      this.setData({ isLoading: false })
      
      // 显示详细错误信息
      let title = '游戏启动失败'
      let content = error.message || '未知错误'
      
      if (error.message.includes('WebGL')) {
        title = '设备不兼容'
        content = '您的设备不支持WebGL 3D渲染，请使用较新的设备或更新微信版本'
      } else if (error.message.includes('着色器')) {
        title = '渲染引擎错误'
        content = '3D渲染器初始化失败，请重启小程序重试'
      } else if (error.message.includes('缓冲区')) {
        title = '内存不足'
        content = '设备内存不足，无法创建3D模型'
      }
      
      wx.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '返回首页',
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  // 等待引擎准备就绪
  waitForEngineReady() {
    return new Promise((resolve, reject) => {
      const checkReady = () => {
        if (this.gameEngine && this.gameEngine.gameState === 'ready') {
          resolve()
        } else if (this.gameEngine && this.gameEngine.gameState === 'error') {
          reject(new Error('引擎初始化失败'))
        } else {
          setTimeout(checkReady, 100)
        }
      }
      checkReady()
    })
  },

  // 启动游戏循环
  startGameLoop() {
    if (this.animationId) return
    
    // 获取Canvas的requestAnimationFrame方法
    const requestAnimationFrame = this.canvas?.requestAnimationFrame || 
                                  wx.requestAnimationFrame || 
                                  ((callback) => setTimeout(callback, 16))
    
    const gameLoop = (currentTime) => {
      if (!this.gameEngine) return
      
      try {
        // 更新游戏逻辑
        this.gameEngine.update(currentTime)
        
        // 渲染游戏
        this.gameEngine.render(currentTime)
        
        // 更新UI数据
        this.updateGameUI()
        
        // 继续下一帧
        this.animationId = requestAnimationFrame(gameLoop)
      } catch (error) {
        console.error('游戏循环错误:', error)
        this.stopGameLoop()
        this.setData({ gameState: 'error' })
      }
    }
    
    this.animationId = requestAnimationFrame(gameLoop)
  },

  // 停止游戏循环
  stopGameLoop() {
    if (this.animationId) {
      const cancelAnimationFrame = this.canvas?.cancelAnimationFrame || 
                                   wx.cancelAnimationFrame || 
                                   clearTimeout
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  },

  // 更新游戏UI
  updateGameUI() {
    if (!this.gameEngine) return
    
    const gameState = this.gameEngine.getGameState()
    
    // 检查状态变化
    if (gameState.state !== this.data.gameState) {
      this.handleGameStateChange(gameState.state)
    }
    
    // 更新数据
    this.setData({
      score: gameState.score,
      snakeLength: gameState.snakeLength,
      fps: gameState.fps,
      gameState: gameState.state
    })
  },

  // 处理游戏状态变化
  handleGameStateChange(newState) {
    switch (newState) {
      case 'gameOver':
        this.handleGameOver()
        break
      case 'playing':
        // 播放开始音效
        this.playSound('start')
        break
      case 'paused':
        // 播放暂停音效
        this.playSound('pause')
        break
    }
  },

  // 处理游戏结束
  handleGameOver() {
    const currentScore = this.data.score
    const bestScore = app.globalData.bestScore
    
    // 检查是否创造新纪录
    const isNewRecord = currentScore > bestScore
    
    if (isNewRecord) {
      app.saveBestScore(currentScore)
      this.setData({ isNewRecord: true })
      
      // 播放新纪录音效
      this.playSound('newRecord')
      
      // 震动反馈
      if (app.globalData.gameSettings.vibrationEnabled) {
        wx.vibrateLong()
      }
    } else {
      // 播放游戏结束音效
      this.playSound('gameOver')
      
      // 震动反馈
      if (app.globalData.gameSettings.vibrationEnabled) {
        wx.vibrateShort()
      }
    }
  },

  // 播放音效
  playSound(type) {
    if (!app.globalData.gameSettings.soundEnabled) return
    
    const soundFiles = {
      start: '/assets/sounds/start.mp3',
      gameOver: '/assets/sounds/gameOver.mp3',
      newRecord: '/assets/sounds/newRecord.mp3',
      pause: '/assets/sounds/pause.mp3',
      eat: '/assets/sounds/eat.mp3'
    }
    
    if (soundFiles[type]) {
      const audio = wx.createInnerAudioContext()
      audio.src = soundFiles[type]
      audio.autoplay = true
      audio.onError((error) => {
        console.warn('音效播放失败:', error)
      })
    }
  },

  // 触摸开始事件
  onTouchStart(e) {
    const touch = e.touches[0]
    this.touchStartPos = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }
    
    // 检查是否触摸摇杆区域
    if (this.data.controlMode === 'joystick') {
      this.handleJoystickStart(touch)
    }
  },

  // 触摸移动事件
  onTouchMove(e) {
    const touch = e.touches[0]
    
    if (this.data.controlMode === 'joystick' && this.data.joystick.active) {
      this.handleJoystickMove(touch)
    }
  },

  // 触摸结束事件
  onTouchEnd(e) {
    if (this.data.controlMode === 'joystick' && this.data.joystick.active) {
      this.handleJoystickEnd()
    } else if (this.touchStartPos) {
      // 检查是否为滑动手势
      this.handleSwipeGesture(e.changedTouches[0])
    }
    
    this.touchStartPos = null
  },

  // 处理摇杆开始
  handleJoystickStart(touch) {
    // 检查触摸点是否在摇杆区域内
    const joystickRect = this.getJoystickRect()
    
    if (this.isPointInRect(touch, joystickRect)) {
      this.setData({
        'joystick.active': true
      })
      
      this.joystickCenter = {
        x: joystickRect.left + joystickRect.width / 2,
        y: joystickRect.top + joystickRect.height / 2
      }
    }
  },

  // 处理摇杆移动
  handleJoystickMove(touch) {
    if (!this.joystickCenter) return
    
    const deltaX = touch.clientX - this.joystickCenter.x
    const deltaY = touch.clientY - this.joystickCenter.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const maxDistance = 70 // 最大拖拽距离（rpx转换为px）
    
    // 限制摇杆范围
    let finalX = deltaX
    let finalY = deltaY
    
    if (distance > maxDistance) {
      finalX = (deltaX / distance) * maxDistance
      finalY = (deltaY / distance) * maxDistance
    }
    
    this.setData({
      'joystick.x': finalX,
      'joystick.y': finalY
    })
    
    // 根据摇杆位置更新游戏方向
    this.updateDirectionFromJoystick(finalX, finalY)
  },

  // 处理摇杆结束
  handleJoystickEnd() {
    this.setData({
      'joystick.active': false,
      'joystick.x': 0,
      'joystick.y': 0
    })
    
    this.joystickCenter = null
  },

  // 根据摇杆位置更新方向
  updateDirectionFromJoystick(x, y) {
    const threshold = 30
    
    if (Math.abs(x) > Math.abs(y)) {
      // 水平方向
      if (x > threshold) {
        this.changeDirection({ x: 1, y: 0, z: 0 }) // 右
      } else if (x < -threshold) {
        this.changeDirection({ x: -1, y: 0, z: 0 }) // 左
      }
    } else {
      // 垂直方向（在3D空间中对应Z轴）
      if (y > threshold) {
        this.changeDirection({ x: 0, y: 0, z: 1 }) // 下（远离相机）
      } else if (y < -threshold) {
        this.changeDirection({ x: 0, y: 0, z: -1 }) // 上（靠近相机）
      }
    }
  },

  // 获取摇杆区域
  getJoystickRect() {
    // 这里需要根据实际布局计算摇杆位置
    // 简化处理，返回固定区域
    const systemInfo = wx.getSystemInfoSync()
    const screenWidth = systemInfo.screenWidth
    const screenHeight = systemInfo.screenHeight
    
    return {
      left: 80 * screenWidth / 750,
      top: screenHeight - 240 * screenHeight / 1334,
      width: 240 * screenWidth / 750,
      height: 240 * screenHeight / 1334
    }
  },

  // 检查点是否在矩形内
  isPointInRect(point, rect) {
    return point.clientX >= rect.left &&
           point.clientX <= rect.left + rect.width &&
           point.clientY >= rect.top &&
           point.clientY <= rect.top + rect.height
  },

  // 处理滑动手势
  handleSwipeGesture(endTouch) {
    if (!this.touchStartPos) return
    
    const deltaX = endTouch.clientX - this.touchStartPos.x
    const deltaY = endTouch.clientY - this.touchStartPos.y
    const deltaTime = Date.now() - this.touchStartPos.time
    
    // 检查是否为有效滑动
    const minDistance = 50
    const maxTime = 500
    
    if (deltaTime > maxTime) return
    
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    if (distance < minDistance) return
    
    // 确定滑动方向
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // 水平滑动
      if (deltaX > 0) {
        this.changeDirection({ x: 1, y: 0, z: 0 }) // 右
      } else {
        this.changeDirection({ x: -1, y: 0, z: 0 }) // 左
      }
    } else {
      // 垂直滑动
      if (deltaY > 0) {
        this.changeDirection({ x: 0, y: 0, z: 1 }) // 下
      } else {
        this.changeDirection({ x: 0, y: 0, z: -1 }) // 上
      }
    }
  },

  // 方向按钮控制
  changeDirection(e) {
    const direction = e.currentTarget?.dataset?.direction || e
    
    const directionMap = {
      up: { x: 0, y: 0, z: -1 },
      down: { x: 0, y: 0, z: 1 },
      left: { x: -1, y: 0, z: 0 },
      right: { x: 1, y: 0, z: 0 }
    }
    
    const directionVector = typeof direction === 'string' ? directionMap[direction] : direction
    
    if (directionVector && this.gameEngine) {
      this.gameEngine.changeDirection(directionVector)
      
      // 震动反馈
      if (app.globalData.gameSettings.vibrationEnabled) {
        wx.vibrateShort({ type: 'light' })
      }
    }
  },

  // 启用重力感应
  enableGyroscope() {
    if (this.data.controlMode !== 'gyroscope') return
    
    wx.startGyroscope({
      interval: 'game',
      success: () => {
        this.gyroscopeListener = wx.onGyroscopeChange((res) => {
          this.handleGyroscopeChange(res)
        })
      }
    })
  },

  // 禁用重力感应
  disableGyroscope() {
    if (this.gyroscopeListener) {
      wx.offGyroscopeChange(this.gyroscopeListener)
      this.gyroscopeListener = null
    }
    
    wx.stopGyroscope()
  },

  // 处理重力感应变化
  handleGyroscopeChange(res) {
    const sensitivity = app.globalData.gameSettings.sensitivity / 10
    const threshold = 0.5 * sensitivity
    
    // 根据陀螺仪数据确定方向
    if (Math.abs(res.x) > Math.abs(res.y)) {
      if (res.x > threshold) {
        this.changeDirection({ x: 1, y: 0, z: 0 })
      } else if (res.x < -threshold) {
        this.changeDirection({ x: -1, y: 0, z: 0 })
      }
    } else {
      if (res.y > threshold) {
        this.changeDirection({ x: 0, y: 0, z: 1 })
      } else if (res.y < -threshold) {
        this.changeDirection({ x: 0, y: 0, z: -1 })
      }
    }
  },

  // 开始游戏
  startGame() {
    if (this.gameEngine) {
      this.gameEngine.startGame()
      this.setData({ gameState: 'playing' })
    }
  },

  // 暂停游戏
  pauseGame() {
    if (this.gameEngine) {
      this.gameEngine.pauseGame()
      this.setData({ gameState: 'paused' })
    }
  },

  // 继续游戏
  resumeGame() {
    if (this.gameEngine) {
      this.gameEngine.resumeGame()
      this.setData({ gameState: 'playing' })
    }
  },

  // 重新开始游戏
  restartGame() {
    if (this.gameEngine) {
      this.gameEngine.resetGame()
      this.setData({
        score: 0,
        snakeLength: 3,
        isNewRecord: false,
        gameState: 'ready'
      })
      
      // 延迟开始
      setTimeout(() => {
        this.startGame()
      }, 500)
    }
  },

  // 退出游戏
  exitGame() {
    wx.navigateBack()
  },

  // 分享成绩
  shareScore() {
    wx.shareAppMessage({
      title: `我在3D贪吃蛇中获得了${this.data.score}分！`,
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.jpg'
    })
  },

  // 清理资源
  cleanup() {
    this.stopGameLoop()
    this.disableGyroscope()
    
    if (this.gameEngine) {
      // 清理WebGL资源
      this.gameEngine = null
    }
  }
}) 
/* 设置页面样式 */
.container {
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-content {
  padding: 40rpx 30rpx;
}

/* 设置组样式 */
.setting-group {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.setting-title {
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

/* 设置项样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  flex-wrap: wrap;
  gap: 20rpx;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 32rpx;
  color: #fff;
  flex: 1;
  min-width: 200rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  width: 100%;
  margin-top: 10rpx;
}

/* 开关样式 */
switch {
  transform: scale(1.2);
}

/* 音量控制 */
.volume-control {
  display: flex;
  align-items: center;
  flex: 2;
  min-width: 300rpx;
  gap: 20rpx;
}

.volume-control slider {
  flex: 1;
}

.volume-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: bold;
  min-width: 60rpx;
}

/* 灵敏度控制 */
.sensitivity-control {
  display: flex;
  align-items: center;
  flex: 2;
  min-width: 300rpx;
  gap: 20rpx;
}

.sensitivity-control slider {
  flex: 1;
}

.sensitivity-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: bold;
  min-width: 40rpx;
}

/* 选项按钮组 */
.intensity-options,
.control-options,
.quality-options,
.difficulty-options {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 20rpx;
}

.intensity-btn,
.control-btn,
.quality-btn,
.difficulty-btn {
  font-size: 24rpx;
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  min-width: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #ccc;
  transition: all 0.3s ease;
}

.intensity-btn.btn-success,
.control-btn.btn-success,
.quality-btn.btn-success,
.difficulty-btn.btn-success {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  border-color: #00f2fe;
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 10rpx 25rpx rgba(79, 172, 254, 0.3);
}

/* 数据显示 */
.best-score,
.game-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 10rpx #00ff88;
}

/* 设置按钮组 */
.setting-buttons {
  display: flex;
  gap: 20rpx;
  width: 100%;
  margin-top: 20rpx;
}

.setting-buttons .btn {
  flex: 1;
  font-size: 28rpx;
  padding: 20rpx;
}

/* 关于内容 */
.about-content {
  padding: 30rpx;
  text-align: center;
}

.game-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 15rpx;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient 3s ease infinite;
}

.version {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 30rpx;
}

.description {
  font-size: 28rpx;
  color: #ccc;
  line-height: 1.6;
  display: block;
  margin-bottom: 40rpx;
}

.about-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.about-buttons .btn {
  font-size: 28rpx;
  padding: 20rpx 30rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.bottom-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 25rpx;
  font-weight: bold;
}

/* 动画效果 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.setting-item {
  transition: all 0.3s ease;
}

.setting-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.setting-group {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .settings-content {
    padding: 30rpx 20rpx;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .setting-label {
    margin-bottom: 20rpx;
    min-width: auto;
  }
  
  .volume-control,
  .sensitivity-control {
    width: 100%;
    min-width: auto;
  }
  
  .intensity-options,
  .control-options,
  .quality-options,
  .difficulty-options {
    margin-top: 0;
  }
  
  .intensity-btn,
  .control-btn,
  .quality-btn,
  .difficulty-btn {
    min-width: 100rpx;
    font-size: 22rpx;
    padding: 12rpx 20rpx;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .about-buttons {
    flex-direction: column;
    gap: 15rpx;
  }
} 
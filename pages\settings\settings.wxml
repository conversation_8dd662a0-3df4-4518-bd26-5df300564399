<view class="container">
  <view class="header">
    <text class="title">游戏设置</text>
  </view>
  
  <view class="settings-content">
    <!-- 音效设置 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">🎵 音效设置</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">背景音乐</text>
        <switch 
          checked="{{settings.soundEnabled}}" 
          bindchange="onSoundChange"
          color="#667eea">
        </switch>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">音效音量</text>
        <view class="volume-control">
          <slider 
            value="{{settings.volume}}" 
            min="0" 
            max="100" 
            bindchange="onVolumeChange"
            activeColor="#667eea"
            backgroundColor="#e0e0e0">
          </slider>
          <text class="volume-text">{{settings.volume}}%</text>
        </view>
      </view>
    </view>
    
    <!-- 震动设置 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">📳 震动反馈</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">启用震动</text>
        <switch 
          checked="{{settings.vibrationEnabled}}" 
          bindchange="onVibrationChange"
          color="#667eea">
        </switch>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">震动强度</text>
        <view class="intensity-options">
          <button 
            class="btn intensity-btn {{settings.vibrationIntensity === 'light' ? 'btn-success' : ''}}"
            data-intensity="light"
            bindtap="selectVibrationIntensity">
            轻微
          </button>
          <button 
            class="btn intensity-btn {{settings.vibrationIntensity === 'medium' ? 'btn-success' : ''}}"
            data-intensity="medium"
            bindtap="selectVibrationIntensity">
            中等
          </button>
          <button 
            class="btn intensity-btn {{settings.vibrationIntensity === 'heavy' ? 'btn-success' : ''}}"
            data-intensity="heavy"
            bindtap="selectVibrationIntensity">
            强烈
          </button>
        </view>
      </view>
    </view>
    
    <!-- 控制设置 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">🎮 控制方式</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">控制模式</text>
        <view class="control-options">
          <button 
            class="btn control-btn {{settings.controlMode === 'joystick' ? 'btn-success' : ''}}"
            data-mode="joystick"
            bindtap="selectControlMode">
            虚拟摇杆
          </button>
          <button 
            class="btn control-btn {{settings.controlMode === 'buttons' ? 'btn-success' : ''}}"
            data-mode="buttons"
            bindtap="selectControlMode">
            方向按钮
          </button>
          <button 
            class="btn control-btn {{settings.controlMode === 'gyroscope' ? 'btn-success' : ''}}"
            data-mode="gyroscope"
            bindtap="selectControlMode">
            重力感应
          </button>
        </view>
      </view>
      
      <view class="setting-item" wx:if="{{settings.controlMode === 'gyroscope'}}">
        <text class="setting-label">灵敏度</text>
        <view class="sensitivity-control">
          <slider 
            value="{{settings.sensitivity}}" 
            min="1" 
            max="10" 
            step="1"
            bindchange="onSensitivityChange"
            activeColor="#667eea"
            backgroundColor="#e0e0e0">
          </slider>
          <text class="sensitivity-text">{{settings.sensitivity}}</text>
        </view>
      </view>
    </view>
    
    <!-- 显示设置 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">🎨 显示设置</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">显示FPS</text>
        <switch 
          checked="{{settings.showFPS}}" 
          bindchange="onShowFPSChange"
          color="#667eea">
        </switch>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">画质</text>
        <view class="quality-options">
          <button 
            class="btn quality-btn {{settings.graphicsQuality === 'low' ? 'btn-success' : ''}}"
            data-quality="low"
            bindtap="selectGraphicsQuality">
            省电
          </button>
          <button 
            class="btn quality-btn {{settings.graphicsQuality === 'medium' ? 'btn-success' : ''}}"
            data-quality="medium"
            bindtap="selectGraphicsQuality">
            平衡
          </button>
          <button 
            class="btn quality-btn {{settings.graphicsQuality === 'high' ? 'btn-success' : ''}}"
            data-quality="high"
            bindtap="selectGraphicsQuality">
            高画质
          </button>
        </view>
      </view>
    </view>
    
    <!-- 游戏设置 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">⚙️ 游戏设置</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">默认难度</text>
        <view class="difficulty-options">
          <button 
            class="btn difficulty-btn {{settings.defaultDifficulty === 'easy' ? 'btn-success' : ''}}"
            data-difficulty="easy"
            bindtap="selectDefaultDifficulty">
            简单
          </button>
          <button 
            class="btn difficulty-btn {{settings.defaultDifficulty === 'normal' ? 'btn-success' : ''}}"
            data-difficulty="normal"
            bindtap="selectDefaultDifficulty">
            普通
          </button>
          <button 
            class="btn difficulty-btn {{settings.defaultDifficulty === 'hard' ? 'btn-success' : ''}}"
            data-difficulty="hard"
            bindtap="selectDefaultDifficulty">
            困难
          </button>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">自动暂停</text>
        <switch 
          checked="{{settings.autoPause}}" 
          bindchange="onAutoPauseChange"
          color="#667eea">
        </switch>
        <text class="setting-desc">切换应用时自动暂停游戏</text>
      </view>
    </view>
    
    <!-- 数据管理 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">💾 数据管理</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">最高分</text>
        <text class="best-score">{{bestScore}}</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">游戏次数</text>
        <text class="game-count">{{gameCount}}</text>
      </view>
      
      <view class="setting-buttons">
        <button class="btn btn-secondary" bindtap="exportData">导出数据</button>
        <button class="btn btn-danger" bindtap="resetData">重置数据</button>
      </view>
    </view>
    
    <!-- 关于信息 -->
    <view class="card setting-group">
      <view class="setting-title">
        <text class="title-text">ℹ️ 关于游戏</text>
      </view>
      
      <view class="about-content">
        <text class="game-name">3D贪吃蛇</text>
        <text class="version">版本 1.0.0</text>
        <text class="description">
          一款基于WebGL技术的3D贪吃蛇游戏，
          提供沉浸式的3D游戏体验和多种创新玩法。
        </text>
        <view class="about-buttons">
          <button class="btn btn-secondary" bindtap="showHelp">游戏帮助</button>
          <button class="btn btn-secondary" bindtap="contactUs">联系我们</button>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="btn btn-danger" bindtap="resetToDefault">恢复默认</button>
    <button class="btn btn-success" bindtap="saveAndExit">保存并返回</button>
  </view>
</view> 
const app = getApp()

Page({
  data: {
    // 当前标签页
    currentTab: 'global', // global, friends
    
    // 时间筛选
    timeFilter: 'all', // all, week, month
    
    // 个人统计
    personalStats: {
      bestScore: 0,
      globalRank: 0,
      gameCount: 0,
      averageScore: 0
    },
    
    // 全球排行榜
    globalLeaderboard: [],
    
    // 好友排行榜
    friendsLeaderboard: [],
    
    // 加载状态
    loading: false,
    hasMore: true,
    
    // 用户详情弹窗
    showUserDetail: false,
    selectedUser: null,
    
    // 用户信息
    userInfo: null
  },

  onLoad() {
    this.initData()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.currentTab === 'global' && this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 初始化数据
  initData() {
    const globalData = app.globalData
    
    this.setData({
      userInfo: globalData.userInfo,
      personalStats: {
        bestScore: globalData.gameData.highScore,
        globalRank: this.calculateGlobalRank(),
        gameCount: globalData.gameData.totalGamesPlayed,
        averageScore: this.calculateAverageScore()
      }
    })
    
    this.loadLeaderboard()
  },

  // 计算全球排名
  calculateGlobalRank() {
    // 这里应该从服务器获取真实排名
    // 暂时使用模拟数据
    const score = app.globalData.gameData.highScore
    if (score === 0) return 0
    
    // 简单的排名估算
    if (score > 1000) return Math.floor(Math.random() * 100) + 1
    if (score > 500) return Math.floor(Math.random() * 500) + 100
    if (score > 100) return Math.floor(Math.random() * 1000) + 500
    return Math.floor(Math.random() * 5000) + 1000
  },

  // 计算平均分
  calculateAverageScore() {
    const gameData = app.globalData.gameData
    if (gameData.totalGamesPlayed === 0) return 0
    
    // 这里应该保存每次游戏的分数历史
    // 暂时使用最高分的估算
    return Math.floor(gameData.highScore * 0.6)
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    
    if (tab === this.data.currentTab) return
    
    this.setData({
      currentTab: tab,
      loading: false,
      hasMore: true
    })
    
    this.loadLeaderboard()
    
    // 震动反馈
    if (app.globalData.gameSettings.vibrationEnabled) {
      wx.vibrateShort()
    }
  },

  // 切换时间筛选
  changeTimeFilter(e) {
    const filter = e.currentTarget.dataset.filter
    
    if (filter === this.data.timeFilter) return
    
    this.setData({
      timeFilter: filter,
      loading: false,
      hasMore: true
    })
    
    this.loadLeaderboard()
  },

  // 加载排行榜数据
  async loadLeaderboard() {
    this.setData({ loading: true })
    
    try {
      if (this.data.currentTab === 'global') {
        await this.loadGlobalLeaderboard()
      } else {
        await this.loadFriendsLeaderboard()
      }
    } catch (error) {
      console.error('加载排行榜失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
    
    this.setData({ loading: false })
  },

  // 加载全球排行榜
  async loadGlobalLeaderboard() {
    // 模拟网络请求延迟
    await this.delay(800)
    
    // 生成模拟数据
    const mockData = this.generateMockGlobalData()
    
    this.setData({
      globalLeaderboard: mockData,
      hasMore: mockData.length >= 20
    })
  },

  // 加载好友排行榜
  async loadFriendsLeaderboard() {
    // 模拟网络请求延迟
    await this.delay(500)
    
    // 生成模拟好友数据
    const mockData = this.generateMockFriendsData()
    
    this.setData({
      friendsLeaderboard: mockData
    })
  },

  // 生成模拟全球排行榜数据
  generateMockGlobalData() {
    const nicknames = [
      '3D蛇王', '贪吃蛇大师', '立体空间', '旋转高手', '蛇神降临',
      '3D征服者', '空间游侠', '立体挑战', '蛇类专家', '维度穿越',
      '贪吃狂魔', '3D传说', '空间主宰', '立体王者', '蛇影忍者',
      '三维大神', '空间猎手', '立体之星', '蛇行天下', '维度霸主'
    ]
    
    const achievements = [
      '🏆 蛇王称号', '⭐ 连胜达人', '🔥 火力全开', '💎 完美操控',
      '🚀 速度之星', '🎯 精准射手', '⚡ 闪电反应', '🌟 传奇玩家'
    ]
    
    const avatars = [
      'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLDUJyegtssKYVMmsb/132',
      'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsadKYVMmsb/132',
      'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLweKYVMmsb/132'
    ]
    
    const data = []
    for (let i = 0; i < 20; i++) {
      const score = Math.floor(Math.random() * 2000) + 500 - (i * 50)
      data.push({
        id: `global_${i}`,
        nickname: nicknames[i % nicknames.length],
        avatarUrl: avatars[i % avatars.length],
        score: Math.max(score, 100),
        rank: i + 1,
        achievement: i < 8 ? achievements[i] : null,
        timeAgo: this.getRandomTimeAgo(),
        gameCount: Math.floor(Math.random() * 500) + 50,
        winRate: Math.floor(Math.random() * 40) + 60,
        joinDate: this.getRandomJoinDate()
      })
    }
    
    return data
  },

  // 生成模拟好友数据
  generateMockFriendsData() {
    if (!app.globalData.userInfo) return []
    
    const friendNames = [
      '小明', '小红', '小李', '小王', '小张',
      '游戏达人', '蛇类爱好者', '3D玩家', '挑战者'
    ]
    
    const data = []
    const friendCount = Math.floor(Math.random() * 8) + 2
    
    for (let i = 0; i < friendCount; i++) {
      const isOnline = Math.random() > 0.7
      data.push({
        id: `friend_${i}`,
        nickname: friendNames[i % friendNames.length],
        avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLDUJyegtssKYVMmsb/132',
        score: Math.floor(Math.random() * 800) + 100,
        isOnline: isOnline,
        lastOnline: isOnline ? null : this.getRandomTimeAgo(),
        gameCount: Math.floor(Math.random() * 200) + 10,
        winRate: Math.floor(Math.random() * 50) + 30,
        joinDate: this.getRandomJoinDate()
      })
    }
    
    // 按分数排序
    data.sort((a, b) => b.score - a.score)
    
    return data
  },

  // 获取随机时间
  getRandomTimeAgo() {
    const times = [
      '1分钟前', '5分钟前', '半小时前', '1小时前', '2小时前',
      '今天', '昨天', '2天前', '3天前', '一周前'
    ]
    return times[Math.floor(Math.random() * times.length)]
  },

  // 获取随机加入日期
  getRandomJoinDate() {
    const days = Math.floor(Math.random() * 365) + 1
    const date = new Date()
    date.setDate(date.getDate() - days)
    return date.toLocaleDateString()
  },

  // 加载更多
  async loadMore() {
    if (this.data.loading || !this.data.hasMore) return
    
    this.setData({ loading: true })
    
    try {
      await this.delay(500)
      
      // 生成更多数据
      const moreData = this.generateMockGlobalData()
      const newData = [...this.data.globalLeaderboard, ...moreData]
      
      this.setData({
        globalLeaderboard: newData,
        hasMore: newData.length < 100 // 最多显示100条
      })
      
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
    
    this.setData({ loading: false })
  },

  // 刷新数据
  async refreshData() {
    // 更新个人统计
    this.setData({
      'personalStats.bestScore': app.globalData.gameData.highScore,
      'personalStats.globalRank': this.calculateGlobalRank(),
      'personalStats.gameCount': app.globalData.gameData.totalGamesPlayed,
      'personalStats.averageScore': this.calculateAverageScore()
    })
    
    // 重新加载排行榜
    await this.loadLeaderboard()
    
    wx.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  },

  // 显示用户详情
  showUserDetail(e) {
    const user = e.currentTarget.dataset.user
    
    // 添加成就数据
    const achievements = [
      { id: 1, icon: '🏆', name: '蛇王称号' },
      { id: 2, icon: '⭐', name: '连胜达人' },
      { id: 3, icon: '🔥', name: '火力全开' }
    ]
    
    this.setData({
      selectedUser: {
        ...user,
        achievements: achievements.slice(0, Math.floor(Math.random() * 3) + 1)
      },
      showUserDetail: true
    })
  },

  // 隐藏用户详情
  hideUserDetail() {
    this.setData({
      showUserDetail: false,
      selectedUser: null
    })
  },

  // 邀请好友
  inviteFriends() {
    wx.shareAppMessage({
      title: '我在玩3D贪吃蛇，快来和我比拼高分！',
      path: '/pages/index/index',
      imageUrl: '/assets/images/invite.jpg'
    })
  },

  // 挑战好友
  challengeFriend(e) {
    e.stopPropagation()
    const user = e.currentTarget.dataset.user
    
    wx.showModal({
      title: '发起挑战',
      content: `向 ${user.nickname} 发起挑战？目标分数：${user.score + 50}`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到游戏页面，设置挑战模式
          wx.navigateTo({
            url: `/pages/game/game?mode=challenge&target=${user.score + 50}&opponent=${user.nickname}`
          })
        }
      }
    })
  },

  // 分享成绩
  shareRank() {
    const personalStats = this.data.personalStats
    
    if (personalStats.bestScore === 0) {
      wx.showToast({
        title: '请先开始游戏',
        icon: 'none'
      })
      return
    }
    
    wx.shareAppMessage({
      title: `我在3D贪吃蛇中获得${personalStats.bestScore}分，全球排名第${personalStats.globalRank}名！`,
      path: '/pages/index/index',
      imageUrl: '/assets/images/rank-share.jpg'
    })
  },

  // 工具函数：延迟
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}) 
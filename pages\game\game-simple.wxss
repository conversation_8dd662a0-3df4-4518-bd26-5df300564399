.simple-game-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.game-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.game-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.status-message {
  display: block;
  font-size: 28rpx;
  color: #666;
  background-color: #f0f8ff;
  padding: 15rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.game-info {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.info-item {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
}

.canvas-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  text-align: center;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: left;
}

.game-canvas {
  display: block;
  margin: 20rpx auto;
  background-color: #000;
  border-radius: 8rpx;
}

.test-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.test-btn:active {
  background-color: #0056b3;
}

.control-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.control-btn {
  border: none;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: white;
}

.start-btn {
  background-color: #28a745;
}

.pause-btn {
  background-color: #ffc107;
  color: #333;
}

.resume-btn {
  background-color: #17a2b8;
}

.restart-btn {
  background-color: #fd7e14;
}

.exit-btn {
  background-color: #dc3545;
}

.control-btn:active {
  opacity: 0.8;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: white;
  font-size: 28rpx;
} 
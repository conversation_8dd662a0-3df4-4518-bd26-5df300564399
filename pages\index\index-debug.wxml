<view class="debug-container">
  <!-- 调试信息显示 -->
  <view class="debug-header">
    <text class="debug-title">3D贪吃蛇 - 调试模式</text>
    <text class="debug-message">{{testMessage}}</text>
  </view>

  <!-- 设备信息 -->
  <view class="debug-section">
    <text class="section-title">设备信息</text>
    <view class="info-item">平台: {{debugInfo.platform}}</view>
    <view class="info-item">型号: {{debugInfo.model}}</view>
    <view class="info-item">微信版本: {{debugInfo.version}}</view>
    <view class="info-item">SDK版本: {{debugInfo.SDKVersion}}</view>
    <view class="info-item">屏幕尺寸: {{debugInfo.screenWidth}}x{{debugInfo.screenHeight}}</view>
    <view class="info-item">像素比: {{debugInfo.pixelRatio}}</view>
    <view class="info-item">性能等级: {{debugInfo.benchmarkLevel}}</view>
  </view>

  <!-- 测试Canvas -->
  <view class="debug-section">
    <text class="section-title">WebGL测试</text>
    <canvas 
      class="test-canvas" 
      type="webgl"
      id="testCanvas"
      style="width: 200px; height: 150px; border: 1px solid #ccc;">
    </canvas>
    <button class="debug-btn" bindtap="testWebGL">测试WebGL支持</button>
  </view>

  <!-- 用户信息 -->
  <view class="debug-section">
    <text class="section-title">用户信息</text>
    <view wx:if="{{userInfo}}">
      <view class="info-item">昵称: {{userInfo.nickName}}</view>
      <view class="info-item">最高分: {{bestScore}}</view>
    </view>
    <button wx:else class="debug-btn" bindtap="login">微信登录</button>
  </view>

  <!-- 游戏控制 -->
  <view class="debug-section">
    <text class="section-title">游戏控制</text>
    
    <!-- 难度选择 -->
    <view class="difficulty-selector">
      <text class="label">难度:</text>
      <button 
        class="difficulty-btn {{difficulty === 'easy' ? 'active' : ''}}" 
        data-difficulty="easy"
        bindtap="selectDifficulty">
        简单
      </button>
      <button 
        class="difficulty-btn {{difficulty === 'normal' ? 'active' : ''}}" 
        data-difficulty="normal"
        bindtap="selectDifficulty">
        普通
      </button>
      <button 
        class="difficulty-btn {{difficulty === 'hard' ? 'active' : ''}}" 
        data-difficulty="hard"
        bindtap="selectDifficulty">
        困难
      </button>
    </view>

    <!-- 主要按钮 -->
    <view class="button-group">
      <button class="main-btn start-btn" bindtap="startGame">开始游戏</button>
      <button class="main-btn" bindtap="showSettings">游戏设置</button>
      <button class="main-btn" bindtap="showLeaderboard">排行榜</button>
    </view>
  </view>

  <!-- 状态显示 -->
  <view class="debug-section">
    <text class="section-title">状态信息</text>
    <view class="status-info">
      <text>当前状态: {{testMessage}}</text>
    </view>
  </view>
</view> 
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.debug-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.debug-message {
  display: block;
  font-size: 28rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 10rpx;
  border-radius: 5rpx;
}

.debug-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 5rpx;
}

.test-canvas {
  display: block;
  margin: 20rpx auto;
  background-color: #000;
}

.debug-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.debug-btn:active {
  background-color: #0056b3;
}

.difficulty-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.difficulty-btn {
  background-color: #f0f0f0;
  color: #666;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 15rpx 25rpx;
  font-size: 26rpx;
  margin-right: 15rpx;
}

.difficulty-btn.active {
  background-color: #007aff;
  color: white;
  border-color: #007aff;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.main-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.start-btn {
  background-color: #ff6b35;
}

.main-btn:active {
  opacity: 0.8;
}

.status-info {
  background-color: #e7f3ff;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.status-info text {
  font-size: 28rpx;
  color: #333;
} 
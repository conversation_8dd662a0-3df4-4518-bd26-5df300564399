const app = getApp()

Page({
  data: {
    userInfo: null,
    bestScore: 0,
    difficulty: 'normal',
    isLoading: true,
    debugInfo: {} // 添加调试信息
  },

  onLoad() {
    console.log('首页加载开始')
    
    // 添加基本的环境检查
    this.checkEnvironment()
    
    // 获取用户信息和游戏数据
    this.initPageData()
    
    // 延迟初始化3D预览，避免阻塞页面
    setTimeout(() => {
      this.init3DPreview()
    }, 1000)
  },

  onShow() {
    // 刷新数据
    this.refreshData()
  },

  onUnload() {
    // 清理3D预览资源
    this.cleanup3DPreview()
  },

  // 检查环境兼容性
  checkEnvironment() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      const debugInfo = {
        platform: systemInfo.platform,
        version: systemInfo.version,
        model: systemInfo.model,
        pixelRatio: systemInfo.pixelRatio,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        benchmarkLevel: systemInfo.benchmarkLevel || '未知'
      }
      
      console.log('设备信息:', debugInfo)
      this.setData({ debugInfo })
      
      // 检查关键版本
      if (systemInfo.version && systemInfo.version < '7.0.0') {
        wx.showModal({
          title: '版本提示',
          content: '微信版本较低，建议更新到最新版本以获得最佳体验',
          showCancel: false
        })
      }
      
    } catch (error) {
      console.error('环境检查失败:', error)
    }
  },

  // 初始化页面数据
  initPageData() {
    const globalData = app.globalData
    this.setData({
      userInfo: globalData.userInfo,
      bestScore: globalData.bestScore,
      difficulty: globalData.gameSettings.difficulty,
      isLoading: true
    })

    // 模拟加载时间
    setTimeout(() => {
      this.setData({
        isLoading: false
      })
    }, 2000)
  },

  // 刷新数据
  refreshData() {
    const globalData = app.globalData
    this.setData({
      userInfo: globalData.userInfo,
      bestScore: globalData.bestScore,
      difficulty: globalData.gameSettings.difficulty
    })
  },

  // 初始化3D预览
  init3DPreview() {
    // 添加延迟和重试机制
    let retryCount = 0
    const maxRetries = 3
    
    const initCanvas = () => {
      const query = wx.createSelectorQuery()
      query.select('#previewCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          console.log('预览Canvas查询结果:', res)
          if (res && res[0] && res[0].node) {
            this.setupCanvas(res[0])
          } else {
            retryCount++
            if (retryCount < maxRetries) {
              console.warn(`预览Canvas获取失败，重试第${retryCount}次...`)
              setTimeout(initCanvas, 200)
            } else {
              console.warn('预览Canvas获取失败，跳过3D预览')
              // 不抛出错误，只是跳过3D预览
            }
          }
        })
    }
    
    // 延迟初始化，确保DOM渲染完成
    setTimeout(initCanvas, 100)
  },

  // 设置Canvas
  setupCanvas(canvasInfo) {
    try {
      console.log('设置预览Canvas...')
      const canvas = canvasInfo.node
      const ctx = canvas.getContext('webgl')
      
      if (!ctx) {
        console.warn('WebGL不支持，跳过3D预览')
        return
      }

      // 设置canvas尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio
      canvas.width = canvasInfo.width * dpr
      canvas.height = canvasInfo.height * dpr
      ctx.viewport(0, 0, canvas.width, canvas.height)

      console.log('开始3D预览动画...')
      // 这里可以集成Three.js进行3D渲染
      // 由于微信小程序的限制，这里先用简单的WebGL实现一个旋转的立方体预览
      this.startPreviewAnimation(ctx, canvas)
    } catch (error) {
      console.error('3D预览初始化失败:', error)
      // 不抛出错误，只是跳过3D预览
    }
  },

  // 开始预览动画
  startPreviewAnimation(ctx, canvas) {
    const vertices = new Float32Array([
      // 前面
      -0.5, -0.5,  0.5,  0.0, 1.0, 0.5,
       0.5, -0.5,  0.5,  0.5, 1.0, 0.0,
       0.5,  0.5,  0.5,  1.0, 0.5, 0.0,
      -0.5,  0.5,  0.5,  0.0, 0.5, 1.0,
      
      // 后面
      -0.5, -0.5, -0.5,  0.5, 0.0, 1.0,
       0.5, -0.5, -0.5,  1.0, 0.0, 0.5,
       0.5,  0.5, -0.5,  0.5, 1.0, 1.0,
      -0.5,  0.5, -0.5,  1.0, 1.0, 0.0
    ])

    const indices = new Uint16Array([
      0, 1, 2,  0, 2, 3,    // 前面
      4, 5, 6,  4, 6, 7,    // 后面
      0, 4, 7,  0, 7, 3,    // 左面
      1, 5, 6,  1, 6, 2,    // 右面
      3, 2, 6,  3, 6, 7,    // 上面
      0, 1, 5,  0, 5, 4     // 下面
    ])

    // 创建着色器程序
    const vertexShaderSource = `
      attribute vec3 a_position;
      attribute vec3 a_color;
      uniform mat4 u_matrix;
      varying vec3 v_color;
      
      void main() {
        gl_Position = u_matrix * vec4(a_position, 1.0);
        v_color = a_color;
      }
    `

    const fragmentShaderSource = `
      precision mediump float;
      varying vec3 v_color;
      
      void main() {
        gl_FragColor = vec4(v_color, 0.8);
      }
    `

    const program = this.createShaderProgram(ctx, vertexShaderSource, fragmentShaderSource)
    if (!program) return

    // 创建缓冲区
    const vertexBuffer = ctx.createBuffer()
    ctx.bindBuffer(ctx.ARRAY_BUFFER, vertexBuffer)
    ctx.bufferData(ctx.ARRAY_BUFFER, vertices, ctx.STATIC_DRAW)

    const indexBuffer = ctx.createBuffer()
    ctx.bindBuffer(ctx.ELEMENT_ARRAY_BUFFER, indexBuffer)
    ctx.bufferData(ctx.ELEMENT_ARRAY_BUFFER, indices, ctx.STATIC_DRAW)

    // 获取attribute和uniform位置
    const positionLocation = ctx.getAttribLocation(program, 'a_position')
    const colorLocation = ctx.getAttribLocation(program, 'a_color')
    const matrixLocation = ctx.getUniformLocation(program, 'u_matrix')

    ctx.enableVertexAttribArray(positionLocation)
    ctx.enableVertexAttribArray(colorLocation)

    // 设置顶点属性
    ctx.vertexAttribPointer(positionLocation, 3, ctx.FLOAT, false, 24, 0)
    ctx.vertexAttribPointer(colorLocation, 3, ctx.FLOAT, false, 24, 12)

    // 启用深度测试和混合
    ctx.enable(ctx.DEPTH_TEST)
    ctx.enable(ctx.BLEND)
    ctx.blendFunc(ctx.SRC_ALPHA, ctx.ONE_MINUS_SRC_ALPHA)

    let rotation = 0
    
    // 获取Canvas的requestAnimationFrame方法
    const requestAnimationFrame = canvas.requestAnimationFrame || 
                                  wx.requestAnimationFrame || 
                                  ((callback) => setTimeout(callback, 16))
    
    const animate = () => {
      rotation += 0.01

      // 清除画布
      ctx.clearColor(0, 0, 0, 0)
      ctx.clear(ctx.COLOR_BUFFER_BIT | ctx.DEPTH_BUFFER_BIT)

      // 创建变换矩阵
      const matrix = this.createMatrix(rotation, canvas.width / canvas.height)

      // 使用着色器程序
      ctx.useProgram(program)
      ctx.uniformMatrix4fv(matrixLocation, false, matrix)

      // 绘制立方体
      ctx.drawElements(ctx.TRIANGLES, indices.length, ctx.UNSIGNED_SHORT, 0)

      this.animationId = requestAnimationFrame(animate)
    }

    animate()
  },

  // 创建着色器程序
  createShaderProgram(ctx, vertexSource, fragmentSource) {
    const vertexShader = this.createShader(ctx, ctx.VERTEX_SHADER, vertexSource)
    const fragmentShader = this.createShader(ctx, ctx.FRAGMENT_SHADER, fragmentSource)
    
    if (!vertexShader || !fragmentShader) return null

    const program = ctx.createProgram()
    ctx.attachShader(program, vertexShader)
    ctx.attachShader(program, fragmentShader)
    ctx.linkProgram(program)

    if (!ctx.getProgramParameter(program, ctx.LINK_STATUS)) {
      console.error('程序链接失败:', ctx.getProgramInfoLog(program))
      return null
    }

    return program
  },

  // 创建着色器
  createShader(ctx, type, source) {
    const shader = ctx.createShader(type)
    ctx.shaderSource(shader, source)
    ctx.compileShader(shader)

    if (!ctx.getShaderParameter(shader, ctx.COMPILE_STATUS)) {
      console.error('着色器编译失败:', ctx.getShaderInfoLog(shader))
      ctx.deleteShader(shader)
      return null
    }

    return shader
  },

  // 创建变换矩阵
  createMatrix(rotation, aspectRatio) {
    const cos = Math.cos(rotation)
    const sin = Math.sin(rotation)
    
    // 透视投影矩阵
    const perspective = [
      1 / aspectRatio, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, -1, -2,
      0, 0, -1, 0
    ]

    // 旋转矩阵 (绕Y轴和X轴)
    const rotationY = [
      cos, 0, sin, 0,
      0, 1, 0, 0,
      -sin, 0, cos, 0,
      0, 0, 0, 1
    ]

    const rotationX = [
      1, 0, 0, 0,
      0, Math.cos(rotation * 0.5), -Math.sin(rotation * 0.5), 0,
      0, Math.sin(rotation * 0.5), Math.cos(rotation * 0.5), 0,
      0, 0, 0, 1
    ]

    // 矩阵相乘
    return this.multiplyMatrices(perspective, this.multiplyMatrices(rotationY, rotationX))
  },

  // 矩阵相乘
  multiplyMatrices(a, b) {
    const result = new Float32Array(16)
    for (let i = 0; i < 4; i++) {
      for (let j = 0; j < 4; j++) {
        let sum = 0
        for (let k = 0; k < 4; k++) {
          sum += a[i * 4 + k] * b[k * 4 + j]
        }
        result[i * 4 + j] = sum
      }
    }
    return result
  },

  // 清理3D预览资源
  cleanup3DPreview() {
    if (this.animationId) {
      // 尝试使用不同的取消方法
      if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(this.animationId)
      } else if (wx.cancelAnimationFrame) {
        wx.cancelAnimationFrame(this.animationId)
      } else {
        clearTimeout(this.animationId)
      }
      this.animationId = null
    }
  },

  // 微信登录
  async login() {
    try {
      wx.showLoading({ title: '登录中...' })
      
      // 使用新的用户主动登录方法
      const userInfo = await app.userLogin()
      
      this.setData({ userInfo })
      wx.hideLoading()
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      
      if (error.message.includes('拒绝')) {
        wx.showToast({
          title: '登录已取消',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        })
      }
      console.error('登录失败:', error)
    }
  },

  // 选择难度
  selectDifficulty(e) {
    const difficulty = e.currentTarget.dataset.difficulty
    this.setData({ difficulty })
    app.globalData.gameSettings.difficulty = difficulty
    app.saveGameSettings()

    // 震动反馈
    if (app.globalData.gameSettings.vibrationEnabled) {
      wx.vibrateShort()
    }
  },

  // 开始游戏
  startGame() {
    // 震动反馈
    if (app.globalData.gameSettings.vibrationEnabled) {
      wx.vibrateShort()
    }

    // 播放音效
    if (app.globalData.gameSettings.soundEnabled) {
      wx.createInnerAudioContext({
        src: '/assets/sounds/button.mp3',
        autoplay: true
      })
    }

    // 跳转到游戏页面
    wx.navigateTo({
      url: `/pages/game/game?difficulty=${this.data.difficulty}`
    })
  },

  // 显示设置页面
  showSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 显示排行榜
  showLeaderboard() {
    wx.navigateTo({
      url: '/pages/leaderboard/leaderboard'
    })
  }
}) 
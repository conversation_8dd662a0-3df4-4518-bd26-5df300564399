const app = getApp()

Page({
  data: {
    gameState: 'loading',
    score: 0,
    snakeLength: 3,
    isLoading: true,
    testMessage: '简化游戏页面加载中...',
    difficulty: 'normal'
  },

  onLoad(options) {
    console.log('简化游戏页面加载开始')
    this.setData({ testMessage: '页面已加载' })
    
    // 获取传入的难度参数
    if (options.difficulty) {
      this.setData({
        difficulty: options.difficulty,
        testMessage: `难度设置为: ${options.difficulty}`
      })
    }
    
    // 简化初始化
    this.initSimpleGame()
  },

  onShow() {
    console.log('简化游戏页面显示')
    this.setData({ testMessage: '页面已显示' })
  },

  onHide() {
    console.log('简化游戏页面隐藏')
  },

  onUnload() {
    console.log('简化游戏页面卸载')
  },

  // 简化的游戏初始化
  initSimpleGame() {
    try {
      console.log('开始简化游戏初始化...')
      
      // 检查设备信息
      const systemInfo = wx.getSystemInfoSync()
      console.log('设备信息:', systemInfo)
      
      // 模拟初始化过程
      setTimeout(() => {
        this.setData({
          isLoading: false,
          gameState: 'ready',
          testMessage: '游戏初始化完成，准备开始'
        })
        console.log('简化游戏初始化完成')
      }, 1000)
      
    } catch (error) {
      console.error('简化游戏初始化失败:', error)
      this.setData({
        isLoading: false,
        gameState: 'error',
        testMessage: '初始化失败: ' + error.message
      })
    }
  },

  // 测试Canvas
  testCanvas() {
    this.setData({ testMessage: '正在测试Canvas...' })
    
    const query = wx.createSelectorQuery()
    query.select('#gameCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        console.log('Canvas查询结果:', res)
        if (res && res[0] && res[0].node) {
          const canvas = res[0].node
          const gl = canvas.getContext('webgl')
          
          if (gl) {
            const version = gl.getParameter(gl.VERSION)
            this.setData({ testMessage: `Canvas正常，WebGL版本: ${version}` })
            console.log('Canvas测试成功，WebGL版本:', version)
          } else {
            this.setData({ testMessage: 'Canvas正常，但WebGL不支持' })
            console.warn('Canvas正常，但WebGL不支持')
          }
        } else {
          this.setData({ testMessage: 'Canvas获取失败' })
          console.error('Canvas获取失败')
        }
      })
  },

  // 测试GameEngine加载
  testGameEngine() {
    this.setData({ testMessage: '正在测试GameEngine...' })
    
    try {
      const GameEngine = require('../../utils/GameEngine.js')
      console.log('GameEngine加载成功:', GameEngine)
      this.setData({ testMessage: 'GameEngine加载成功' })
    } catch (error) {
      console.error('GameEngine加载失败:', error)
      this.setData({ testMessage: 'GameEngine加载失败: ' + error.message })
    }
  },

  // 开始游戏
  startGame() {
    this.setData({ 
      gameState: 'playing',
      testMessage: '游戏开始！（简化版本）'
    })
    console.log('简化游戏开始')
  },

  // 暂停游戏
  pauseGame() {
    this.setData({ 
      gameState: 'paused',
      testMessage: '游戏暂停'
    })
    console.log('简化游戏暂停')
  },

  // 继续游戏
  resumeGame() {
    this.setData({ 
      gameState: 'playing',
      testMessage: '游戏继续'
    })
    console.log('简化游戏继续')
  },

  // 重新开始游戏
  restartGame() {
    this.setData({
      score: 0,
      snakeLength: 3,
      gameState: 'ready',
      testMessage: '游戏重置完成'
    })
    console.log('简化游戏重置')
  },

  // 退出游戏
  exitGame() {
    console.log('退出简化游戏')
    wx.navigateBack()
  }
}) 
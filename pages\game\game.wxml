<view class="game-container">
  <!-- 3D游戏画布 -->
  <canvas 
    class="game-canvas" 
    type="webgl"
    id="gameCanvas"
    disable-scroll="true"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd">
  </canvas>
  
  <!-- 游戏UI层 -->
  <view class="game-ui">
    <!-- HUD信息显示 -->
    <view class="hud">
      <view class="score-display">
        分数: {{score}}
      </view>
      <view class="controls">
        <button class="btn-icon pause-btn interactive" bindtap="pauseGame" wx:if="{{gameState === 'playing'}}">
          ⏸️
        </button>
        <button class="btn-icon resume-btn interactive" bindtap="resumeGame" wx:if="{{gameState === 'paused'}}">
          ▶️
        </button>
      </view>
      <view class="game-info">
        <text class="length-display">长度: {{snakeLength}}</text>
      </view>
    </view>
    
    <!-- 虚拟摇杆 -->
    <view class="joystick-container interactive" wx:if="{{controlMode === 'joystick'}}">
      <view class="joystick-base">
        <view 
          class="joystick-stick" 
          style="transform: translate({{joystick.x}}rpx, {{joystick.y}}rpx)">
        </view>
      </view>
    </view>
    
    <!-- 方向控制按钮 -->
    <view class="direction-controls interactive" wx:if="{{controlMode === 'buttons'}}">
      <view class="direction-row">
        <button class="direction-btn" data-direction="up" bindtap="changeDirection">↑</button>
      </view>
      <view class="direction-row">
        <button class="direction-btn" data-direction="left" bindtap="changeDirection">←</button>
        <button class="direction-btn" data-direction="right" bindtap="changeDirection">→</button>
      </view>
      <view class="direction-row">
        <button class="direction-btn" data-direction="down" bindtap="changeDirection">↓</button>
      </view>
    </view>
    
    <!-- 暂停菜单 -->
    <view class="pause-menu" wx:if="{{gameState === 'paused'}}">
      <view class="pause-content card">
        <text class="pause-title">游戏暂停</text>
        <view class="pause-buttons">
          <button class="btn btn-success interactive" bindtap="resumeGame">继续游戏</button>
          <button class="btn btn-secondary interactive" bindtap="restartGame">重新开始</button>
          <button class="btn btn-danger interactive" bindtap="exitGame">退出游戏</button>
        </view>
      </view>
    </view>
    
    <!-- 游戏结束菜单 -->
    <view class="game-over-menu" wx:if="{{gameState === 'gameOver'}}">
      <view class="game-over-content card">
        <text class="game-over-title">游戏结束</text>
        <view class="final-score">
          <text class="score-label">最终分数</text>
          <text class="score-value">{{score}}</text>
          <text class="best-score-label" wx:if="{{isNewRecord}}">🎉 新纪录！</text>
        </view>
        <view class="game-over-buttons">
          <button class="btn btn-success interactive" bindtap="restartGame">再来一局</button>
          <button class="btn btn-secondary interactive" bindtap="shareScore">分享成绩</button>
          <button class="btn btn-danger interactive" bindtap="exitGame">返回主页</button>
        </view>
      </view>
    </view>
    
    <!-- 加载提示 -->
    <view class="loading-overlay" wx:if="{{isLoading}}">
      <view class="loading"></view>
      <text class="loading-text">正在初始化3D游戏场景...</text>
    </view>
    
    <!-- FPS显示（调试用） -->
    <view class="fps-display" wx:if="{{showFPS}}">
      FPS: {{fps}}
    </view>
  </view>
</view> 
/* 帮助页面样式 */
.container {
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
  min-height: 100vh;
  color: #fff;
}

/* 页面头部 */
.header {
  text-align: center;
  padding: 40rpx 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient 3s ease infinite;
}

/* 内容区域 */
.content {
  height: calc(100vh - 200rpx);
  padding: 40rpx 30rpx 140rpx;
}

/* 章节样式 */
.section {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  overflow: hidden;
  position: relative;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 300% 300%;
  animation: gradient 2s ease infinite;
}

.section-title {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.section-content {
  padding: 30rpx;
}

/* 描述文字 */
.desc {
  font-size: 28rpx;
  line-height: 1.6;
  color: #ccc;
}

/* 规则项目 */
.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rule-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  min-width: 40rpx;
}

.rule-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #ccc;
  flex: 1;
}

/* 控制方式 */
.control-item {
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.control-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.control-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.control-desc {
  font-size: 26rpx;
  line-height: 1.5;
  color: #999;
}

/* 难度等级 */
.difficulty-item {
  margin-bottom: 25rpx;
  padding: 25rpx;
  border-radius: 15rpx;
  border: 2px solid;
  position: relative;
  overflow: hidden;
}

.difficulty-item.easy {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.difficulty-item.normal {
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
}

.difficulty-item.hard {
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
}

.diff-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.diff-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.diff-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.diff-desc {
  font-size: 26rpx;
  line-height: 1.5;
  color: #ccc;
}

/* 游戏技巧 */
.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tip-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 10rpx;
}

.tip-desc {
  font-size: 26rpx;
  line-height: 1.5;
  color: #999;
}

/* 特殊功能 */
.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  min-width: 40rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  line-height: 1.5;
  color: #999;
}

/* 常见问题 */
.faq-item {
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.faq-question {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
  display: block;
  margin-bottom: 15rpx;
}

.faq-answer {
  font-size: 26rpx;
  line-height: 1.6;
  color: #ccc;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.bottom-actions .btn {
  flex: 1;
  font-size: 32rpx;
  padding: 25rpx;
  font-weight: bold;
}

/* 动画效果 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.section {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .content {
    padding: 30rpx 20rpx 140rpx;
  }
  
  .section-content {
    padding: 20rpx;
  }
  
  .tip-item,
  .control-item,
  .difficulty-item,
  .faq-item {
    padding: 20rpx;
  }
  
  .tip-number {
    width: 50rpx;
    height: 50rpx;
    font-size: 24rpx;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 15rpx;
  }
} 
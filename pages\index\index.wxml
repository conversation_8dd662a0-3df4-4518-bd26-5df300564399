<view class="container">
  <!-- 背景3D预览 -->
  <canvas 
    class="preview-canvas" 
    type="webgl"
    id="previewCanvas"
    disable-scroll="true">
  </canvas>
  
  <!-- 主界面内容 -->
  <view class="main-content">
    <!-- 游戏标题 -->
    <view class="title float">3D贪吃蛇</view>
    
    <!-- 用户信息卡片 -->
    <view class="card user-info" wx:if="{{userInfo}}">
      <image class="avatar" src="{{userInfo.avatarUrl}}" />
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="best-score">最高分: {{bestScore}}</text>
      </view>
    </view>
    
    <!-- 登录提示 -->
    <view class="card login-prompt" wx:else>
      <text class="subtitle">登录后可保存游戏记录</text>
      <button class="btn btn-primary" bindtap="login">微信登录</button>
    </view>
    
    <!-- 游戏预览信息 -->
    <view class="card game-preview">
      <view class="preview-info">
        <text class="feature-title">游戏特色</text>
        <view class="feature-list">
          <text class="feature-item">🎮 全3D立体视角</text>
          <text class="feature-item">🌟 炫酷粒子特效</text>
          <text class="feature-item">🎵 沉浸式音效</text>
          <text class="feature-item">🏆 全球排行榜</text>
        </view>
      </view>
    </view>
    
    <!-- 主要按钮组 -->
    <view class="button-group">
      <button class="btn btn-success start-btn pulse" bindtap="startGame">
        开始游戏
      </button>
      
      <view class="secondary-buttons">
        <button class="btn btn-secondary" bindtap="showSettings">
          游戏设置
        </button>
        <button class="btn btn-secondary" bindtap="showLeaderboard">
          排行榜
        </button>
      </view>
    </view>
    
    <!-- 难度选择 -->
    <view class="card difficulty-selector">
      <text class="subtitle">选择难度</text>
      <view class="difficulty-options">
        <button 
          class="btn {{difficulty === 'easy' ? 'btn-success' : ''}}" 
          data-difficulty="easy"
          bindtap="selectDifficulty">
          简单
        </button>
        <button 
          class="btn {{difficulty === 'normal' ? 'btn-success' : ''}}" 
          data-difficulty="normal"
          bindtap="selectDifficulty">
          普通
        </button>
        <button 
          class="btn {{difficulty === 'hard' ? 'btn-success' : ''}}" 
          data-difficulty="hard"
          bindtap="selectDifficulty">
          困难
        </button>
      </view>
    </view>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text class="loading-text">正在加载3D资源...</text>
  </view>
</view> 
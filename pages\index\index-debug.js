const app = getApp()

Page({
  data: {
    userInfo: null,
    bestScore: 0,
    difficulty: 'normal',
    isLoading: false,
    debugInfo: {},
    testMessage: '页面加载中...'
  },

  onLoad() {
    console.log('调试版首页加载开始')
    this.setData({ testMessage: '页面已加载' })
    
    // 基本环境检查
    this.checkEnvironment()
    
    // 简化的数据初始化
    this.initPageData()
  },

  onShow() {
    console.log('调试版首页显示')
    this.setData({ testMessage: '页面已显示' })
  },

  // 检查环境兼容性
  checkEnvironment() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      const debugInfo = {
        platform: systemInfo.platform,
        version: systemInfo.version,
        model: systemInfo.model,
        pixelRatio: systemInfo.pixelRatio,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        benchmarkLevel: systemInfo.benchmarkLevel || '未知',
        SDKVersion: systemInfo.SDKVersion
      }
      
      console.log('设备信息:', debugInfo)
      this.setData({ 
        debugInfo,
        testMessage: `设备: ${systemInfo.model}, 微信版本: ${systemInfo.version}`
      })
      
    } catch (error) {
      console.error('环境检查失败:', error)
      this.setData({ testMessage: '环境检查失败: ' + error.message })
    }
  },

  // 简化的数据初始化
  initPageData() {
    try {
      const globalData = app.globalData
      this.setData({
        userInfo: globalData.userInfo,
        bestScore: globalData.gameData?.highScore || 0,
        difficulty: globalData.gameSettings?.difficulty || 'normal',
        isLoading: false
      })
      
      console.log('数据初始化完成')
      this.setData({ testMessage: '数据初始化完成' })
    } catch (error) {
      console.error('数据初始化失败:', error)
      this.setData({ testMessage: '数据初始化失败: ' + error.message })
    }
  },

  // 测试WebGL支持
  testWebGL() {
    this.setData({ testMessage: '正在测试WebGL...' })
    
    const query = wx.createSelectorQuery()
    query.select('#testCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        console.log('Canvas查询结果:', res)
        if (res && res[0] && res[0].node) {
          const canvas = res[0].node
          const gl = canvas.getContext('webgl')
          
          if (gl) {
            const version = gl.getParameter(gl.VERSION)
            this.setData({ testMessage: `WebGL支持: ${version}` })
            console.log('WebGL版本:', version)
          } else {
            this.setData({ testMessage: 'WebGL不支持' })
            console.warn('WebGL不支持')
          }
        } else {
          this.setData({ testMessage: 'Canvas获取失败' })
          console.error('Canvas获取失败')
        }
      })
  },

  // 简化的登录
  async login() {
    try {
      this.setData({ testMessage: '正在登录...' })
      wx.showLoading({ title: '登录中...' })
      
      const userInfo = await app.userLogin()
      
      this.setData({ 
        userInfo,
        testMessage: '登录成功: ' + userInfo.nickName
      })
      wx.hideLoading()
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      this.setData({ testMessage: '登录失败: ' + error.message })
      
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      })
      console.error('登录失败:', error)
    }
  },

  // 选择难度
  selectDifficulty(e) {
    const difficulty = e.currentTarget.dataset.difficulty
    this.setData({ 
      difficulty,
      testMessage: '难度已设置为: ' + difficulty
    })
    
    if (app.globalData.gameSettings) {
      app.globalData.gameSettings.difficulty = difficulty
      app.saveGameSettings()
    }
  },

  // 开始游戏
  startGame() {
    this.setData({ testMessage: '正在跳转到游戏页面...' })
    
    // 震动反馈
    try {
      if (app.globalData.gameSettings?.vibrationEnabled) {
        wx.vibrateShort()
      }
    } catch (error) {
      console.warn('震动反馈失败:', error)
    }

    // 先尝试跳转到简化游戏页面
    wx.navigateTo({
      url: `/pages/game/game-simple?difficulty=${this.data.difficulty}`,
      success: () => {
        console.log('跳转到简化游戏页面成功')
        this.setData({ testMessage: '跳转到简化游戏页面成功' })
      },
      fail: (error) => {
        console.error('跳转到简化游戏页面失败:', error)
        this.setData({ testMessage: '跳转失败: ' + error.errMsg })
        
        // 如果简化页面也失败，尝试原始游戏页面
        wx.navigateTo({
          url: `/pages/game/game?difficulty=${this.data.difficulty}`,
          success: () => {
            console.log('跳转到原始游戏页面成功')
            this.setData({ testMessage: '跳转到原始游戏页面成功' })
          },
          fail: (error2) => {
            console.error('跳转到原始游戏页面也失败:', error2)
            this.setData({ testMessage: '所有游戏页面跳转都失败' })
            wx.showToast({
              title: '跳转失败',
              icon: 'error'
            })
          }
        })
      }
    })
  },

  // 显示设置页面
  showSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 显示排行榜
  showLeaderboard() {
    wx.navigateTo({
      url: '/pages/leaderboard/leaderboard'
    })
  }
}) 
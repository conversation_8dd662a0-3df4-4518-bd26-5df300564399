App({
  globalData: {
    userInfo: null,
    gameSettings: {
      soundEnabled: true,
      vibrationEnabled: true,
      difficulty: 'normal',
      controlType: 'joystick',
      graphicsQuality: 'medium'
    },
    gameData: {
      highScore: 0,
      totalGamesPlayed: 0,
      totalPlayTime: 0,
      achievements: []
    },
    isDebug: false
  },

  onLaunch() {
    console.log('3D贪吃蛇游戏启动');
    
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        env: 'your-env-id', // 请替换为您的云开发环境ID
        traceUser: true
      });
      console.log('云开发初始化成功');
    }

    // 加载用户设置
    this.loadSettings();
    
    // 获取用户信息
    this.getUserInfo();
    
    // 初始化游戏数据
    this.loadGameData();
  },

  onShow() {
    console.log('应用进入前台');
  },

  onHide() {
    console.log('应用进入后台');
    // 保存游戏数据
    this.saveGameData();
  },

  // 获取用户信息
  getUserInfo() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userInfo']) {
          wx.getUserInfo({
            success: (res) => {
              this.globalData.userInfo = res.userInfo;
              console.log('用户信息获取成功', res.userInfo);
            },
            fail: (err) => {
              console.error('获取用户信息失败', err);
            }
          });
        }
      }
    });
  },

  // 微信登录方法
  async wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            console.log('wx.login成功，code:', loginRes.code)
            
            // 检查是否已有用户信息授权
            wx.getSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.userInfo']) {
                  // 已授权，直接获取用户信息
                  wx.getUserInfo({
                    success: (userRes) => {
                      console.log('获取用户信息成功:', userRes.userInfo)
                      this.globalData.userInfo = userRes.userInfo
                      resolve(userRes.userInfo)
                    },
                    fail: (err) => {
                      console.error('获取用户信息失败:', err)
                      reject(new Error('获取用户信息失败'))
                    }
                  })
                } else {
                  // 未授权，需要用户主动授权
                  // 注意：getUserProfile必须在用户点击事件中调用
                  console.log('用户未授权，需要主动授权')
                  resolve(null) // 返回null表示需要用户主动登录
                }
              },
              fail: (err) => {
                console.error('获取用户设置失败:', err)
                reject(new Error('获取用户设置失败'))
              }
            })
          } else {
            reject(new Error('微信登录失败'))
          }
        },
        fail: (err) => {
          console.error('wx.login失败:', err)
          reject(new Error('微信登录失败'))
        }
      })
    })
  },

  // 用户主动登录（必须在点击事件中调用）
  async userLogin() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('用户主动授权成功:', res.userInfo)
          this.globalData.userInfo = res.userInfo
          resolve(res.userInfo)
        },
        fail: (err) => {
          console.error('用户拒绝授权:', err)
          reject(new Error('用户拒绝授权'))
        }
      })
    })
  },

  // 加载游戏设置
  loadSettings() {
    try {
      const settings = wx.getStorageSync('gameSettings');
      if (settings) {
        this.globalData.gameSettings = { ...this.globalData.gameSettings, ...settings };
        console.log('游戏设置加载成功', this.globalData.gameSettings);
      }
    } catch (err) {
      console.error('加载游戏设置失败', err);
    }
  },

  // 保存游戏设置
  saveSettings() {
    try {
      wx.setStorageSync('gameSettings', this.globalData.gameSettings);
      console.log('游戏设置保存成功');
    } catch (err) {
      console.error('保存游戏设置失败', err);
    }
  },

  // 加载游戏数据
  loadGameData() {
    try {
      const gameData = wx.getStorageSync('gameData');
      if (gameData) {
        this.globalData.gameData = { ...this.globalData.gameData, ...gameData };
        console.log('游戏数据加载成功', this.globalData.gameData);
      }
    } catch (err) {
      console.error('加载游戏数据失败', err);
    }
  },

  // 保存游戏数据
  saveGameData() {
    try {
      wx.setStorageSync('gameData', this.globalData.gameData);
      console.log('游戏数据保存成功');
    } catch (err) {
      console.error('保存游戏数据失败', err);
    }
  },

  // 更新最高分
  updateHighScore(score) {
    if (score > this.globalData.gameData.highScore) {
      this.globalData.gameData.highScore = score;
      this.saveGameData();
      
      // 触发震动反馈
      if (this.globalData.gameSettings.vibrationEnabled) {
        wx.vibrateShort();
      }
      
      return true; // 新纪录
    }
    return false;
  },

  // 记录游戏完成
  recordGameComplete(score, playTime) {
    this.globalData.gameData.totalGamesPlayed++;
    this.globalData.gameData.totalPlayTime += playTime;
    this.updateHighScore(score);
    this.saveGameData();
  },

  // 获取设置
  getSetting(key) {
    return this.globalData.gameSettings[key];
  },

  // 更新设置
  updateSetting(key, value) {
    this.globalData.gameSettings[key] = value;
    this.saveSettings();
  },

  // 重置游戏数据
  resetGameData() {
    this.globalData.gameData = {
      highScore: 0,
      totalGamesPlayed: 0,
      totalPlayTime: 0,
      achievements: []
    };
    this.saveGameData();
  },

  // 分享游戏
  shareGame(score = null) {
    const title = score 
      ? `我在3D贪吃蛇中获得了${score}分！` 
      : '快来体验这款超炫的3D贪吃蛇游戏！';
    
    return {
      title: title,
      path: '/pages/index/index',
      imageUrl: '/images/share.png' // 需要添加分享图片
    };
  },

  // 保存最高分（兼容方法）
  saveBestScore(score) {
    return this.updateHighScore(score);
  },

  // 保存游戏设置（兼容方法）
  saveGameSettings() {
    this.saveSettings();
  },

  // 获取最高分（兼容属性）
  get bestScore() {
    return this.globalData.gameData.highScore;
  }
});
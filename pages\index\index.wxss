/* 主页面样式 */
.container {
  position: relative;
  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  overflow: hidden;
}

/* 3D预览画布 */
.preview-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.3;
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  padding: 40rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

/* 用户信息卡片 */
.user-info {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 30rpx;
  min-width: 300rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.best-score {
  font-size: 24rpx;
  color: #00ff88;
  text-shadow: 0 0 10rpx #00ff88;
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 30rpx;
}

/* 游戏预览信息 */
.game-preview {
  background: rgba(0, 255, 136, 0.1);
  border: 2px solid rgba(0, 255, 136, 0.3);
  margin-bottom: 30rpx;
}

.feature-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #00ff88;
  display: block;
  margin-bottom: 20rpx;
  text-align: center;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.feature-item {
  font-size: 28rpx;
  color: #ccc;
  display: block;
  text-align: left;
  padding: 5rpx 0;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  margin: 40rpx 0;
}

.start-btn {
  font-size: 40rpx;
  padding: 30rpx 80rpx;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2rpx;
  box-shadow: 0 15rpx 35rpx rgba(79, 172, 254, 0.4);
}

.secondary-buttons {
  display: flex;
  gap: 20rpx;
}

.secondary-buttons .btn {
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}

/* 难度选择 */
.difficulty-selector {
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid rgba(102, 126, 234, 0.3);
  margin-bottom: 40rpx;
}

.difficulty-options {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
  justify-content: center;
}

.difficulty-options .btn {
  font-size: 24rpx;
  padding: 15rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #ccc;
  transition: all 0.3s ease;
}

.difficulty-options .btn-success {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  border-color: #00f2fe;
  color: #fff;
  transform: scale(1.1);
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-text {
  color: #667eea;
  font-size: 32rpx;
  margin-top: 40rpx;
  text-align: center;
}

/* 特效增强 */
.start-btn:active {
  transform: scale(0.95);
}

.card:hover {
  transform: translateY(-5rpx);
  box-shadow: 0 25rpx 60rpx rgba(0, 0, 0, 0.4);
}

/* 背景装饰 */
.container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
  animation: rotate 20s linear infinite;
  z-index: 1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .main-content {
    padding: 30rpx;
  }
  
  .start-btn {
    font-size: 36rpx;
    padding: 25rpx 60rpx;
  }
  
  .secondary-buttons {
    flex-direction: column;
    width: 100%;
    gap: 15rpx;
  }
  
  .secondary-buttons .btn {
    width: 100%;
  }
  
  .difficulty-options {
    flex-wrap: wrap;
    gap: 10rpx;
  }
  
  .user-info {
    min-width: auto;
    width: 100%;
  }
} 
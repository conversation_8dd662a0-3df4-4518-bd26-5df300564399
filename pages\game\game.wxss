/* 游戏页面样式 */
.game-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
  overflow: hidden;
}

/* 3D游戏画布 */
.game-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
}

/* 游戏UI层 */
.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* HUD信息显示 */
.hud {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
}

.score-display {
  background: rgba(0, 0, 0, 0.7);
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 15rpx #00ff88;
  border: 2px solid rgba(0, 255, 136, 0.3);
  backdrop-filter: blur(10px);
}

.controls {
  display: flex;
  gap: 20rpx;
}

.btn-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  pointer-events: auto;
}

.btn-icon:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.2);
}

.game-info {
  background: rgba(0, 0, 0, 0.7);
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  color: #4facfe;
  text-shadow: 0 0 10rpx #4facfe;
  border: 2px solid rgba(79, 172, 254, 0.3);
  backdrop-filter: blur(10px);
}

/* 虚拟摇杆 */
.joystick-container {
  position: absolute;
  bottom: 120rpx;
  left: 80rpx;
  width: 240rpx;
  height: 240rpx;
  pointer-events: auto;
}

.joystick-base {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 0 30rpx rgba(255, 255, 255, 0.1),
    inset 0 0 30rpx rgba(255, 255, 255, 0.05);
}

.joystick-stick {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 
    0 10rpx 25rpx rgba(0, 0, 0, 0.4),
    0 0 20rpx rgba(102, 126, 234, 0.5);
  transition: all 0.1s ease;
  cursor: pointer;
}

.joystick-stick:active {
  transform: translate(-50%, -50%) scale(1.1);
}

/* 方向控制按钮 */
.direction-controls {
  position: absolute;
  bottom: 120rpx;
  right: 80rpx;
  pointer-events: auto;
}

.direction-row {
  display: flex;
  justify-content: center;
  gap: 15rpx;
  margin: 10rpx 0;
}

.direction-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.direction-btn:active {
  transform: scale(0.9);
  background: rgba(102, 126, 234, 0.3);
  box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.5);
}

/* 暂停菜单 */
.pause-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  backdrop-filter: blur(15px);
}

.pause-content {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 60rpx;
  border-radius: 30rpx;
  text-align: center;
  backdrop-filter: blur(20px);
  box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.5);
}

.pause-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 40rpx;
  display: block;
}

.pause-buttons {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.pause-buttons .btn {
  min-width: 300rpx;
  padding: 25rpx 40rpx;
  font-size: 32rpx;
}

/* 游戏结束菜单 */
.game-over-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  backdrop-filter: blur(20px);
}

.game-over-content {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 80rpx;
  border-radius: 40rpx;
  text-align: center;
  backdrop-filter: blur(25px);
  box-shadow: 0 40rpx 100rpx rgba(0, 0, 0, 0.6);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.game-over-title {
  font-size: 56rpx;
  font-weight: bold;
  color: #ff416c;
  margin-bottom: 40rpx;
  display: block;
  text-shadow: 0 0 20rpx #ff416c;
}

.final-score {
  margin: 40rpx 0;
  padding: 30rpx;
  background: rgba(0, 255, 136, 0.1);
  border-radius: 20rpx;
  border: 2px solid rgba(0, 255, 136, 0.3);
}

.score-label {
  font-size: 32rpx;
  color: #ccc;
  display: block;
  margin-bottom: 15rpx;
}

.score-value {
  font-size: 72rpx;
  font-weight: bold;
  color: #00ff88;
  display: block;
  text-shadow: 0 0 25rpx #00ff88;
  margin-bottom: 15rpx;
}

.best-score-label {
  font-size: 36rpx;
  color: #ffd700;
  font-weight: bold;
  display: block;
  text-shadow: 0 0 20rpx #ffd700;
  animation: pulse 2s ease-in-out infinite;
}

.game-over-buttons {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 50rpx;
}

.game-over-buttons .btn {
  min-width: 350rpx;
  padding: 30rpx 45rpx;
  font-size: 36rpx;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  z-index: 100;
}

.loading-text {
  color: #667eea;
  font-size: 36rpx;
  margin-top: 50rpx;
  text-align: center;
  text-shadow: 0 0 15rpx #667eea;
}

/* FPS显示 */
.fps-display {
  position: absolute;
  top: 200rpx;
  right: 40rpx;
  background: rgba(0, 0, 0, 0.7);
  padding: 15rpx 20rpx;
  border-radius: 15rpx;
  font-size: 24rpx;
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
  backdrop-filter: blur(10px);
}

/* 特效增强 */
.score-display,
.game-info {
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20rpx rgba(0, 255, 136, 0.3);
  }
  to {
    box-shadow: 0 0 40rpx rgba(0, 255, 136, 0.6);
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .hud {
    top: 30rpx;
    left: 30rpx;
    right: 30rpx;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .joystick-container {
    bottom: 100rpx;
    left: 60rpx;
    width: 200rpx;
    height: 200rpx;
  }
  
  .direction-controls {
    bottom: 100rpx;
    right: 60rpx;
  }
  
  .direction-btn {
    width: 70rpx;
    height: 70rpx;
    font-size: 32rpx;
  }
  
  .pause-content,
  .game-over-content {
    padding: 50rpx;
    margin: 40rpx;
  }
  
  .game-over-buttons .btn,
  .pause-buttons .btn {
    min-width: 250rpx;
    font-size: 30rpx;
  }
} 
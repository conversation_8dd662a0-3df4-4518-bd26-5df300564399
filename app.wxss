/* 全局样式 */
page {
  background-color: #000;
  color: #fff;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 通用类 */
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  color: #fff;
  background: linear-gradient(45deg, #667eea, #764ba2);
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.btn-secondary {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.btn-success {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.btn-danger {
  background: linear-gradient(45deg, #ff416c, #ff4b2b);
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.3);
}

/* 标题样式 */
.title {
  font-size: 60rpx;
  font-weight: bold;
  text-align: center;
  margin: 40rpx 0;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient 3s ease infinite;
}

.subtitle {
  font-size: 36rpx;
  font-weight: 600;
  margin: 20rpx 0;
  color: #ccc;
}

/* 动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.float {
  animation: float 3s ease-in-out infinite;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 加载动画 */
.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 游戏界面通用样式 */
.game-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.game-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.game-ui .interactive {
  pointer-events: auto;
}

/* HUD样式 */
.hud {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.score-display {
  background: rgba(0, 0, 0, 0.5);
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 10rpx #00ff88;
}

/* 虚拟摇杆样式 */
.joystick-container {
  position: absolute;
  bottom: 100rpx;
  left: 100rpx;
  width: 200rpx;
  height: 200rpx;
}

.joystick-base {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.joystick-stick {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.3);
  transition: all 0.1s ease;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .title {
    font-size: 48rpx;
  }
  
  .btn {
    font-size: 28rpx;
    padding: 16rpx 32rpx;
  }
  
  .card {
    padding: 30rpx;
    margin: 15rpx;
  }
} 
Page({
  data: {},

  onLoad() {
    // 页面初始化
  },

  onShow() {
    // 页面显示时
  },

  // 返回游戏
  backToGame() {
    wx.navigateTo({
      url: '/pages/game/game'
    })
  },

  // 前往设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '3D贪吃蛇游戏攻略 - 快来学习游戏技巧！',
      path: '/pages/help/help',
      imageUrl: '/assets/images/help-share.jpg'
    }
  }
}) 
/**
 * 3D贪吃蛇游戏引擎
 * 基于WebGL实现的3D渲染引擎
 */

class GameEngine {
  constructor(canvas) {
    this.canvas = canvas
    
    // 改进WebGL上下文获取和检查
    this.gl = null
    try {
      // 尝试获取WebGL上下文
      this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!this.gl) {
        throw new Error('当前设备不支持WebGL。请使用支持WebGL的设备。')
      }
      
      // 检查WebGL版本和扩展
      const version = this.gl.getParameter(this.gl.VERSION)
      console.log('WebGL版本:', version)
      
      // 检查关键扩展
      const extensions = [
        'OES_element_index_uint',
        'OES_standard_derivatives'
      ]
      
      extensions.forEach(ext => {
        const extension = this.gl.getExtension(ext)
        if (!extension) {
          console.warn(`WebGL扩展 ${ext} 不受支持`)
        }
      })
      
    } catch (error) {
      console.error('WebGL初始化失败:', error.message)
      throw new Error(`WebGL初始化失败: ${error.message}`)
    }

    // 游戏状态
    this.gameState = 'loading'
    this.score = 0
    this.difficulty = 'normal'
    
    // 3D场景参数
    this.camera = {
      position: [0, 15, 25],
      target: [0, 0, 0],
      up: [0, 1, 0],
      fov: 45,
      near: 0.1,
      far: 100
    }
    
    // 蛇身数据
    this.snake = {
      body: [
        { x: 0, y: 0, z: 0 },
        { x: -1, y: 0, z: 0 },
        { x: -2, y: 0, z: 0 }
      ],
      direction: { x: 1, y: 0, z: 0 },
      nextDirection: { x: 1, y: 0, z: 0 },
      speed: 200, // 移动间隔(ms)
      lastMoveTime: 0
    }
    
    // 食物数据
    this.food = {
      position: { x: 5, y: 0, z: 5 },
      rotation: 0,
      type: 'normal' // normal, special, bonus
    }
    
    // 游戏场地
    this.gameField = {
      width: 20,
      height: 20,
      depth: 20,
      walls: []
    }
    
    // 障碍物
    this.obstacles = []
    
    // 粒子系统
    this.particles = []
    
    // 渲染缓冲区
    this.buffers = {}
    this.shaders = {}
    
    // 性能监控
    this.frameCount = 0
    this.lastFPSTime = 0
    this.fps = 60
    
    this.init()
  }

  // 初始化游戏引擎
  async init() {
    try {
      console.log('开始初始化游戏引擎...')
      
      console.log('1. 设置WebGL...')
      this.setupWebGL()
      
      console.log('2. 加载着色器...')
      await this.loadShaders()
      
      console.log('3. 创建缓冲区...')
      this.createBuffers()
      
      console.log('4. 生成游戏场地...')
      this.generateGameField()
      
      console.log('5. 生成食物...')
      this.generateFood()
      
      console.log('游戏引擎初始化成功!')
      this.gameState = 'ready'
    } catch (error) {
      console.error('游戏引擎初始化失败:', error)
      this.gameState = 'error'
      
      // 提供详细的错误信息
      let errorMessage = '游戏引擎初始化失败'
      if (error.message.includes('WebGL')) {
        errorMessage = '设备不支持WebGL 3D渲染'
      } else if (error.message.includes('着色器')) {
        errorMessage = '3D着色器编译失败'
      } else if (error.message.includes('缓冲区')) {
        errorMessage = '3D几何体创建失败'
      }
      
      throw new Error(errorMessage)
    }
  }

  // 设置WebGL
  setupWebGL() {
    const gl = this.gl
    
    // 设置视口
    gl.viewport(0, 0, this.canvas.width, this.canvas.height)
    
    // 启用深度测试
    gl.enable(gl.DEPTH_TEST)
    gl.depthFunc(gl.LEQUAL)
    
    // 启用面剔除
    gl.enable(gl.CULL_FACE)
    gl.cullFace(gl.BACK)
    
    // 启用混合
    gl.enable(gl.BLEND)
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA)
    
    // 设置清除颜色
    gl.clearColor(0.0, 0.0, 0.0, 1.0)
  }

  // 加载着色器
  async loadShaders() {
    // 基础着色器
    const basicVertexShader = `
      attribute vec3 a_position;
      attribute vec3 a_normal;
      attribute vec2 a_texCoord;
      
      uniform mat4 u_modelMatrix;
      uniform mat4 u_viewMatrix;
      uniform mat4 u_projectionMatrix;
      uniform mat4 u_normalMatrix;
      
      varying vec3 v_position;
      varying vec3 v_normal;
      varying vec2 v_texCoord;
      
      void main() {
        vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);
        v_position = worldPosition.xyz;
        v_normal = normalize((u_normalMatrix * vec4(a_normal, 0.0)).xyz);
        v_texCoord = a_texCoord;
        
        gl_Position = u_projectionMatrix * u_viewMatrix * worldPosition;
      }
    `

    const basicFragmentShader = `
      precision mediump float;
      
      uniform vec3 u_color;
      uniform vec3 u_lightPosition;
      uniform vec3 u_lightColor;
      uniform vec3 u_cameraPosition;
      uniform float u_shininess;
      uniform float u_opacity;
      
      varying vec3 v_position;
      varying vec3 v_normal;
      varying vec2 v_texCoord;
      
      void main() {
        vec3 normal = normalize(v_normal);
        vec3 lightDirection = normalize(u_lightPosition - v_position);
        vec3 viewDirection = normalize(u_cameraPosition - v_position);
        vec3 reflectDirection = reflect(-lightDirection, normal);
        
        // 环境光
        vec3 ambient = 0.3 * u_lightColor;
        
        // 漫反射
        float diff = max(dot(normal, lightDirection), 0.0);
        vec3 diffuse = diff * u_lightColor;
        
        // 镜面反射
        float spec = pow(max(dot(viewDirection, reflectDirection), 0.0), u_shininess);
        vec3 specular = spec * u_lightColor;
        
        vec3 finalColor = (ambient + diffuse + specular) * u_color;
        gl_FragColor = vec4(finalColor, u_opacity);
      }
    `

    // 粒子着色器
    const particleVertexShader = `
      attribute vec3 a_position;
      attribute float a_size;
      attribute vec4 a_color;
      
      uniform mat4 u_viewMatrix;
      uniform mat4 u_projectionMatrix;
      uniform float u_time;
      
      varying vec4 v_color;
      
      void main() {
        vec4 viewPosition = u_viewMatrix * vec4(a_position, 1.0);
        gl_Position = u_projectionMatrix * viewPosition;
        gl_PointSize = a_size * (1.0 / -viewPosition.z);
        v_color = a_color;
      }
    `

    const particleFragmentShader = `
      precision mediump float;
      
      varying vec4 v_color;
      
      void main() {
        vec2 coord = gl_PointCoord - 0.5;
        float dist = length(coord);
        if (dist > 0.5) discard;
        
        float alpha = 1.0 - (dist * 2.0);
        gl_FragColor = vec4(v_color.rgb, v_color.a * alpha);
      }
    `

    this.shaders.basic = this.createShaderProgram(basicVertexShader, basicFragmentShader)
    this.shaders.particle = this.createShaderProgram(particleVertexShader, particleFragmentShader)
  }

  // 创建着色器程序
  createShaderProgram(vertexSource, fragmentSource) {
    const gl = this.gl
    
    try {
      const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexSource)
      const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentSource)
      
      const program = gl.createProgram()
      gl.attachShader(program, vertexShader)
      gl.attachShader(program, fragmentShader)
      gl.linkProgram(program)
      
      if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const info = gl.getProgramInfoLog(program)
        console.error('着色器程序链接失败:', info)
        throw new Error(`着色器程序链接失败: ${info}`)
      }
      
      return program
    } catch (error) {
      console.error('创建着色器程序失败:', error)
      throw new Error(`着色器编译失败: ${error.message}`)
    }
  }

  // 创建着色器
  createShader(type, source) {
    const gl = this.gl
    const shader = gl.createShader(type)
    
    gl.shaderSource(shader, source)
    gl.compileShader(shader)
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      const info = gl.getShaderInfoLog(shader)
      const shaderType = type === gl.VERTEX_SHADER ? '顶点着色器' : '片段着色器'
      console.error(`${shaderType}编译失败:`, info)
      console.error('着色器源码:', source)
      gl.deleteShader(shader)
      throw new Error(`${shaderType}编译失败: ${info}`)
    }
    
    return shader
  }

  // 创建几何体缓冲区
  createBuffers() {
    // 立方体几何体
    this.buffers.cube = this.createCubeBuffers()
    // 球体几何体
    this.buffers.sphere = this.createSphereBuffers()
    // 平面几何体
    this.buffers.plane = this.createPlaneBuffers()
  }

  // 创建立方体缓冲区
  createCubeBuffers() {
    const vertices = new Float32Array([
      // 前面
      -0.5, -0.5,  0.5,  0, 0, 1,  0, 0,
       0.5, -0.5,  0.5,  0, 0, 1,  1, 0,
       0.5,  0.5,  0.5,  0, 0, 1,  1, 1,
      -0.5,  0.5,  0.5,  0, 0, 1,  0, 1,
      
      // 后面
      -0.5, -0.5, -0.5,  0, 0, -1,  1, 0,
       0.5, -0.5, -0.5,  0, 0, -1,  0, 0,
       0.5,  0.5, -0.5,  0, 0, -1,  0, 1,
      -0.5,  0.5, -0.5,  0, 0, -1,  1, 1,
      
      // 左面
      -0.5, -0.5, -0.5, -1, 0, 0,  0, 0,
      -0.5, -0.5,  0.5, -1, 0, 0,  1, 0,
      -0.5,  0.5,  0.5, -1, 0, 0,  1, 1,
      -0.5,  0.5, -0.5, -1, 0, 0,  0, 1,
      
      // 右面
       0.5, -0.5, -0.5,  1, 0, 0,  1, 0,
       0.5, -0.5,  0.5,  1, 0, 0,  0, 0,
       0.5,  0.5,  0.5,  1, 0, 0,  0, 1,
       0.5,  0.5, -0.5,  1, 0, 0,  1, 1,
      
      // 上面
      -0.5,  0.5, -0.5,  0, 1, 0,  0, 1,
      -0.5,  0.5,  0.5,  0, 1, 0,  0, 0,
       0.5,  0.5,  0.5,  0, 1, 0,  1, 0,
       0.5,  0.5, -0.5,  0, 1, 0,  1, 1,
      
      // 下面
      -0.5, -0.5, -0.5,  0, -1, 0,  0, 0,
      -0.5, -0.5,  0.5,  0, -1, 0,  0, 1,
       0.5, -0.5,  0.5,  0, -1, 0,  1, 1,
       0.5, -0.5, -0.5,  0, -1, 0,  1, 0
    ])

    const indices = new Uint16Array([
      0,  1,  2,    0,  2,  3,    // 前面
      4,  5,  6,    4,  6,  7,    // 后面
      8,  9,  10,   8,  10, 11,   // 左面
      12, 13, 14,   12, 14, 15,   // 右面
      16, 17, 18,   16, 18, 19,   // 上面
      20, 21, 22,   20, 22, 23    // 下面
    ])

    return this.createGeometryBuffers(vertices, indices)
  }

  // 创建球体缓冲区
  createSphereBuffers() {
    const latitudeBands = 16
    const longitudeBands = 16
    const vertices = []
    const indices = []

    for (let lat = 0; lat <= latitudeBands; lat++) {
      const theta = lat * Math.PI / latitudeBands
      const sinTheta = Math.sin(theta)
      const cosTheta = Math.cos(theta)

      for (let lon = 0; lon <= longitudeBands; lon++) {
        const phi = lon * 2 * Math.PI / longitudeBands
        const sinPhi = Math.sin(phi)
        const cosPhi = Math.cos(phi)

        const x = cosPhi * sinTheta
        const y = cosTheta
        const z = sinPhi * sinTheta
        const u = 1 - (lon / longitudeBands)
        const v = 1 - (lat / latitudeBands)

        vertices.push(x * 0.5, y * 0.5, z * 0.5, x, y, z, u, v)
      }
    }

    for (let lat = 0; lat < latitudeBands; lat++) {
      for (let lon = 0; lon < longitudeBands; lon++) {
        const first = (lat * (longitudeBands + 1)) + lon
        const second = first + longitudeBands + 1

        indices.push(first, second, first + 1)
        indices.push(second, second + 1, first + 1)
      }
    }

    return this.createGeometryBuffers(new Float32Array(vertices), new Uint16Array(indices))
  }

  // 创建平面缓冲区
  createPlaneBuffers() {
    const vertices = new Float32Array([
      -0.5, 0, -0.5,  0, 1, 0,  0, 0,
       0.5, 0, -0.5,  0, 1, 0,  1, 0,
       0.5, 0,  0.5,  0, 1, 0,  1, 1,
      -0.5, 0,  0.5,  0, 1, 0,  0, 1
    ])

    const indices = new Uint16Array([0, 1, 2, 0, 2, 3])

    return this.createGeometryBuffers(vertices, indices)
  }

  // 创建几何体缓冲区
  createGeometryBuffers(vertices, indices) {
    const gl = this.gl

    const vertexBuffer = gl.createBuffer()
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer)
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW)

    const indexBuffer = gl.createBuffer()
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer)
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW)

    return {
      vertex: vertexBuffer,
      index: indexBuffer,
      indexCount: indices.length
    }
  }

  // 生成游戏场地
  generateGameField() {
    this.gameField.walls = []
    const size = this.gameField.width / 2

    // 生成边界墙壁
    for (let x = -size; x <= size; x++) {
      for (let z = -size; z <= size; z++) {
        if (x === -size || x === size || z === -size || z === size) {
          this.gameField.walls.push({ x, y: 0, z })
        }
      }
    }

    // 根据难度生成障碍物
    this.generateObstacles()
  }

  // 生成障碍物
  generateObstacles() {
    this.obstacles = []
    const obstacleCount = {
      easy: 5,
      normal: 10,
      hard: 15
    }[this.difficulty] || 10

    for (let i = 0; i < obstacleCount; i++) {
      let position
      do {
        position = {
          x: Math.floor(Math.random() * (this.gameField.width - 4)) - this.gameField.width / 2 + 2,
          y: 0,
          z: Math.floor(Math.random() * (this.gameField.depth - 4)) - this.gameField.depth / 2 + 2
        }
      } while (this.isPositionOccupied(position))

      this.obstacles.push({
        ...position,
        type: 'static',
        rotation: Math.random() * Math.PI * 2
      })
    }
  }

  // 生成食物
  generateFood() {
    let position
    do {
      position = {
        x: Math.floor(Math.random() * (this.gameField.width - 2)) - this.gameField.width / 2 + 1,
        y: 0,
        z: Math.floor(Math.random() * (this.gameField.depth - 2)) - this.gameField.depth / 2 + 1
      }
    } while (this.isPositionOccupied(position))

    this.food.position = position
    this.food.rotation = 0
  }

  // 检查位置是否被占用
  isPositionOccupied(position) {
    // 检查蛇身
    for (const segment of this.snake.body) {
      if (segment.x === position.x && segment.z === position.z) {
        return true
      }
    }

    // 检查障碍物
    for (const obstacle of this.obstacles) {
      if (obstacle.x === position.x && obstacle.z === position.z) {
        return true
      }
    }

    // 检查墙壁
    for (const wall of this.gameField.walls) {
      if (wall.x === position.x && wall.z === position.z) {
        return true
      }
    }

    return false
  }

  // 更新游戏逻辑
  update(currentTime) {
    if (this.gameState !== 'playing') return

    // 更新蛇身移动
    this.updateSnakeMovement(currentTime)
    
    // 更新食物动画
    this.updateFood(currentTime)
    
    // 更新粒子系统
    this.updateParticles(currentTime)
    
    // 更新障碍物动画
    this.updateObstacles(currentTime)
  }

  // 更新蛇身移动
  updateSnakeMovement(currentTime) {
    if (currentTime - this.snake.lastMoveTime >= this.snake.speed) {
      // 更新方向
      this.snake.direction = { ...this.snake.nextDirection }
      
      // 计算新头部位置
      const head = this.snake.body[0]
      const newHead = {
        x: head.x + this.snake.direction.x,
        y: head.y + this.snake.direction.y,
        z: head.z + this.snake.direction.z
      }

      // 碰撞检测
      if (this.checkCollision(newHead)) {
        this.gameOver()
        return
      }

      // 移动蛇身
      this.snake.body.unshift(newHead)

      // 检查是否吃到食物
      if (this.checkFoodCollision(newHead)) {
        this.eatFood()
      } else {
        this.snake.body.pop()
      }

      this.snake.lastMoveTime = currentTime
    }
  }

  // 碰撞检测
  checkCollision(position) {
    // 检查边界
    const halfSize = this.gameField.width / 2
    if (position.x < -halfSize || position.x > halfSize ||
        position.z < -halfSize || position.z > halfSize) {
      return true
    }

    // 检查自身碰撞
    for (let i = 1; i < this.snake.body.length; i++) {
      const segment = this.snake.body[i]
      if (segment.x === position.x && segment.z === position.z) {
        return true
      }
    }

    // 检查障碍物碰撞
    for (const obstacle of this.obstacles) {
      if (obstacle.x === position.x && obstacle.z === position.z) {
        return true
      }
    }

    return false
  }

  // 检查食物碰撞
  checkFoodCollision(position) {
    return position.x === this.food.position.x && position.z === this.food.position.z
  }

  // 吃食物
  eatFood() {
    this.score += 10
    this.generateFood()
    this.createEatEffect()
  }

  // 创建吃食物特效
  createEatEffect() {
    const position = this.food.position
    for (let i = 0; i < 10; i++) {
      this.particles.push({
        position: {
          x: position.x + (Math.random() - 0.5) * 2,
          y: position.y + Math.random() * 2,
          z: position.z + (Math.random() - 0.5) * 2
        },
        velocity: {
          x: (Math.random() - 0.5) * 4,
          y: Math.random() * 4 + 2,
          z: (Math.random() - 0.5) * 4
        },
        color: [Math.random(), Math.random() + 0.5, 0.2, 1.0],
        size: Math.random() * 10 + 5,
        life: 1.0,
        decay: 0.02
      })
    }
  }

  // 更新食物动画
  updateFood(currentTime) {
    this.food.rotation += 0.02
  }

  // 更新粒子系统
  updateParticles(currentTime) {
    this.particles = this.particles.filter(particle => {
      particle.position.x += particle.velocity.x * 0.016
      particle.position.y += particle.velocity.y * 0.016
      particle.position.z += particle.velocity.z * 0.016
      
      particle.velocity.y -= 9.8 * 0.016 // 重力
      particle.life -= particle.decay
      
      return particle.life > 0
    })
  }

  // 更新障碍物动画
  updateObstacles(currentTime) {
    this.obstacles.forEach(obstacle => {
      if (obstacle.type === 'rotating') {
        obstacle.rotation += 0.01
      }
    })
  }

  // 改变蛇的方向
  changeDirection(direction) {
    if (this.gameState !== 'playing') return

    const current = this.snake.direction
    
    // 防止反向移动
    if ((direction.x !== 0 && current.x !== -direction.x) ||
        (direction.z !== 0 && current.z !== -direction.z)) {
      this.snake.nextDirection = direction
    }
  }

  // 游戏结束
  gameOver() {
    this.gameState = 'gameOver'
  }

  // 开始游戏
  startGame() {
    this.gameState = 'playing'
    this.snake.lastMoveTime = Date.now()
  }

  // 暂停游戏
  pauseGame() {
    this.gameState = 'paused'
  }

  // 继续游戏
  resumeGame() {
    this.gameState = 'playing'
    this.snake.lastMoveTime = Date.now()
  }

  // 重置游戏
  resetGame() {
    this.snake.body = [
      { x: 0, y: 0, z: 0 },
      { x: -1, y: 0, z: 0 },
      { x: -2, y: 0, z: 0 }
    ]
    this.snake.direction = { x: 1, y: 0, z: 0 }
    this.snake.nextDirection = { x: 1, y: 0, z: 0 }
    this.score = 0
    this.particles = []
    this.generateFood()
    this.gameState = 'ready'
  }

  // 设置难度
  setDifficulty(difficulty) {
    this.difficulty = difficulty
    this.snake.speed = {
      easy: 300,
      normal: 200,
      hard: 100
    }[difficulty] || 200
    
    this.generateObstacles()
  }

  // 渲染游戏
  render(currentTime) {
    const gl = this.gl
    
    // 清除缓冲区
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT)
    
    // 计算矩阵
    const matrices = this.calculateMatrices()
    
    // 渲染游戏场地
    this.renderGameField(matrices)
    
    // 渲染蛇身
    this.renderSnake(matrices)
    
    // 渲染食物
    this.renderFood(matrices)
    
    // 渲染障碍物
    this.renderObstacles(matrices)
    
    // 渲染粒子
    this.renderParticles(matrices)
    
    // 更新FPS
    this.updateFPS(currentTime)
  }

  // 计算变换矩阵
  calculateMatrices() {
    const aspect = this.canvas.width / this.canvas.height
    
    // 投影矩阵
    const projectionMatrix = this.createPerspectiveMatrix(
      this.camera.fov * Math.PI / 180,
      aspect,
      this.camera.near,
      this.camera.far
    )
    
    // 视图矩阵
    const viewMatrix = this.createLookAtMatrix(
      this.camera.position,
      this.camera.target,
      this.camera.up
    )
    
    return { projectionMatrix, viewMatrix }
  }

  // 渲染蛇身
  renderSnake(matrices) {
    const gl = this.gl
    const shader = this.shaders.basic
    
    gl.useProgram(shader)
    this.bindBasicShaderUniforms(shader, matrices)
    
    this.snake.body.forEach((segment, index) => {
      const modelMatrix = this.createModelMatrix(
        [segment.x, segment.y, segment.z],
        [0, 0, 0],
        [0.8, 0.8, 0.8]
      )
      
      const color = index === 0 ? [0.2, 1.0, 0.4] : [0.0, 0.8, 0.2] // 头部颜色不同
      
      gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_modelMatrix'), false, modelMatrix)
      gl.uniform3fv(gl.getUniformLocation(shader, 'u_color'), color)
      
      this.renderGeometry(this.buffers.cube)
    })
  }

  // 渲染食物
  renderFood(matrices) {
    const gl = this.gl
    const shader = this.shaders.basic
    
    gl.useProgram(shader)
    this.bindBasicShaderUniforms(shader, matrices)
    
    const modelMatrix = this.createModelMatrix(
      [this.food.position.x, this.food.position.y + 0.5, this.food.position.z],
      [0, this.food.rotation, 0],
      [0.6, 0.6, 0.6]
    )
    
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_modelMatrix'), false, modelMatrix)
    gl.uniform3fv(gl.getUniformLocation(shader, 'u_color'), [1.0, 0.2, 0.2])
    
    this.renderGeometry(this.buffers.sphere)
  }

  // 渲染游戏场地
  renderGameField(matrices) {
    const gl = this.gl
    const shader = this.shaders.basic
    
    gl.useProgram(shader)
    this.bindBasicShaderUniforms(shader, matrices)
    
    // 渲染地面
    const groundMatrix = this.createModelMatrix(
      [0, -0.5, 0],
      [0, 0, 0],
      [this.gameField.width, 1, this.gameField.depth]
    )
    
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_modelMatrix'), false, groundMatrix)
    gl.uniform3fv(gl.getUniformLocation(shader, 'u_color'), [0.1, 0.1, 0.2])
    
    this.renderGeometry(this.buffers.plane)
    
    // 渲染墙壁
    this.gameField.walls.forEach(wall => {
      const modelMatrix = this.createModelMatrix(
        [wall.x, wall.y, wall.z],
        [0, 0, 0],
        [1, 2, 1]
      )
      
      gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_modelMatrix'), false, modelMatrix)
      gl.uniform3fv(gl.getUniformLocation(shader, 'u_color'), [0.3, 0.3, 0.4])
      
      this.renderGeometry(this.buffers.cube)
    })
  }

  // 渲染障碍物
  renderObstacles(matrices) {
    const gl = this.gl
    const shader = this.shaders.basic
    
    gl.useProgram(shader)
    this.bindBasicShaderUniforms(shader, matrices)
    
    this.obstacles.forEach(obstacle => {
      const modelMatrix = this.createModelMatrix(
        [obstacle.x, obstacle.y, obstacle.z],
        [0, obstacle.rotation, 0],
        [0.8, 0.8, 0.8]
      )
      
      gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_modelMatrix'), false, modelMatrix)
      gl.uniform3fv(gl.getUniformLocation(shader, 'u_color'), [0.8, 0.4, 0.1])
      
      this.renderGeometry(this.buffers.cube)
    })
  }

  // 渲染粒子
  renderParticles(matrices) {
    if (this.particles.length === 0) return
    
    const gl = this.gl
    const shader = this.shaders.particle
    
    gl.useProgram(shader)
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_viewMatrix'), false, matrices.viewMatrix)
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_projectionMatrix'), false, matrices.projectionMatrix)
    
    // 渲染每个粒子
    this.particles.forEach(particle => {
      // 这里简化处理，实际应该使用粒子系统批量渲染
      gl.uniform3fv(gl.getUniformLocation(shader, 'u_position'), [
        particle.position.x, particle.position.y, particle.position.z
      ])
      gl.uniform4fv(gl.getUniformLocation(shader, 'u_color'), particle.color)
      gl.uniform1f(gl.getUniformLocation(shader, 'u_size'), particle.size)
      
      // 简化：直接绘制点
      gl.drawArrays(gl.POINTS, 0, 1)
    })
  }

  // 绑定基础着色器uniforms
  bindBasicShaderUniforms(shader, matrices) {
    const gl = this.gl
    
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_viewMatrix'), false, matrices.viewMatrix)
    gl.uniformMatrix4fv(gl.getUniformLocation(shader, 'u_projectionMatrix'), false, matrices.projectionMatrix)
    
    // 光照参数
    gl.uniform3fv(gl.getUniformLocation(shader, 'u_lightPosition'), [10, 20, 10])
    gl.uniform3fv(gl.getUniformLocation(shader, 'u_lightColor'), [1, 1, 1])
    gl.uniform3fv(gl.getUniformLocation(shader, 'u_cameraPosition'), this.camera.position)
    gl.uniform1f(gl.getUniformLocation(shader, 'u_shininess'), 32.0)
    gl.uniform1f(gl.getUniformLocation(shader, 'u_opacity'), 1.0)
  }

  // 渲染几何体
  renderGeometry(geometry) {
    const gl = this.gl
    const shader = this.shaders.basic
    
    // 绑定顶点缓冲区
    gl.bindBuffer(gl.ARRAY_BUFFER, geometry.vertex)
    
    // 设置顶点属性
    const positionLocation = gl.getAttribLocation(shader, 'a_position')
    const normalLocation = gl.getAttribLocation(shader, 'a_normal')
    const texCoordLocation = gl.getAttribLocation(shader, 'a_texCoord')
    
    gl.enableVertexAttribArray(positionLocation)
    gl.enableVertexAttribArray(normalLocation)
    gl.enableVertexAttribArray(texCoordLocation)
    
    // 顶点数据布局：position(3) + normal(3) + texCoord(2) = 8 floats
    const stride = 8 * 4 // 8 floats * 4 bytes per float
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0)
    gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12)
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, stride, 24)
    
    // 绑定索引缓冲区并绘制
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, geometry.index)
    gl.drawElements(gl.TRIANGLES, geometry.indexCount, gl.UNSIGNED_SHORT, 0)
  }

  // 创建模型矩阵
  createModelMatrix(translation, rotation, scale) {
    const matrix = new Float32Array(16)
    
    // 简化的矩阵计算，实际项目中建议使用成熟的数学库
    this.identity(matrix)
    this.translate(matrix, translation)
    this.rotateY(matrix, rotation[1])
    this.scaleMatrix(matrix, scale)
    
    return matrix
  }

  // 创建透视投影矩阵
  createPerspectiveMatrix(fov, aspect, near, far) {
    const matrix = new Float32Array(16)
    const f = 1.0 / Math.tan(fov / 2)
    const rangeInv = 1 / (near - far)
    
    matrix[0] = f / aspect
    matrix[1] = 0
    matrix[2] = 0
    matrix[3] = 0
    matrix[4] = 0
    matrix[5] = f
    matrix[6] = 0
    matrix[7] = 0
    matrix[8] = 0
    matrix[9] = 0
    matrix[10] = (near + far) * rangeInv
    matrix[11] = -1
    matrix[12] = 0
    matrix[13] = 0
    matrix[14] = near * far * rangeInv * 2
    matrix[15] = 0
    
    return matrix
  }

  // 创建视图矩阵
  createLookAtMatrix(eye, target, up) {
    const matrix = new Float32Array(16)
    
    // 计算视图方向向量
    const zAxis = this.normalize([eye[0] - target[0], eye[1] - target[1], eye[2] - target[2]])
    const xAxis = this.normalize(this.cross(up, zAxis))
    const yAxis = this.cross(zAxis, xAxis)
    
    matrix[0] = xAxis[0]
    matrix[1] = yAxis[0]
    matrix[2] = zAxis[0]
    matrix[3] = 0
    matrix[4] = xAxis[1]
    matrix[5] = yAxis[1]
    matrix[6] = zAxis[1]
    matrix[7] = 0
    matrix[8] = xAxis[2]
    matrix[9] = yAxis[2]
    matrix[10] = zAxis[2]
    matrix[11] = 0
    matrix[12] = -this.dot(xAxis, eye)
    matrix[13] = -this.dot(yAxis, eye)
    matrix[14] = -this.dot(zAxis, eye)
    matrix[15] = 1
    
    return matrix
  }

  // 数学工具函数
  identity(matrix) {
    matrix.fill(0)
    matrix[0] = matrix[5] = matrix[10] = matrix[15] = 1
  }

  translate(matrix, translation) {
    matrix[12] += translation[0]
    matrix[13] += translation[1]
    matrix[14] += translation[2]
  }

  rotateY(matrix, angle) {
    const c = Math.cos(angle)
    const s = Math.sin(angle)
    const temp = [...matrix]
    
    matrix[0] = temp[0] * c + temp[8] * s
    matrix[2] = temp[2] * c + temp[10] * s
    matrix[8] = temp[8] * c - temp[0] * s
    matrix[10] = temp[10] * c - temp[2] * s
  }

  scaleMatrix(matrix, scale) {
    matrix[0] *= scale[0]
    matrix[5] *= scale[1]
    matrix[10] *= scale[2]
  }

  normalize(vec) {
    const length = Math.sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2])
    return length > 0 ? [vec[0] / length, vec[1] / length, vec[2] / length] : [0, 0, 0]
  }

  cross(a, b) {
    return [
      a[1] * b[2] - a[2] * b[1],
      a[2] * b[0] - a[0] * b[2],
      a[0] * b[1] - a[1] * b[0]
    ]
  }

  dot(a, b) {
    return a[0] * b[0] + a[1] * b[1] + a[2] * b[2]
  }

  // 更新FPS
  updateFPS(currentTime) {
    this.frameCount++
    if (currentTime - this.lastFPSTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastFPSTime = currentTime
    }
  }

  // 获取游戏状态
  getGameState() {
    return {
      state: this.gameState,
      score: this.score,
      snakeLength: this.snake.body.length,
      fps: this.fps
    }
  }
  
  // 静态方法：诊断设备兼容性
  static diagnoseCompatibility(canvas) {
    const result = {
      webglSupported: false,
      webglVersion: null,
      extensions: [],
      errors: [],
      warnings: []
    }
    
    try {
      // 检查WebGL支持
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      
      if (!gl) {
        result.errors.push('设备不支持WebGL')
        return result
      }
      
      result.webglSupported = true
      result.webglVersion = gl.getParameter(gl.VERSION)
      
      // 检查关键扩展
      const requiredExtensions = [
        'OES_element_index_uint',
        'OES_standard_derivatives'
      ]
      
      requiredExtensions.forEach(ext => {
        const extension = gl.getExtension(ext)
        if (extension) {
          result.extensions.push(ext)
        } else {
          result.warnings.push(`扩展 ${ext} 不受支持，可能影响性能`)
        }
      })
      
      // 检查WebGL限制
      const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE)
      const maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS)
      const maxVaryingVectors = gl.getParameter(gl.MAX_VARYING_VECTORS)
      
      if (maxTextureSize < 1024) {
        result.warnings.push('最大纹理尺寸较小，可能影响画质')
      }
      
      if (maxVertexAttribs < 8) {
        result.warnings.push('顶点属性数量受限，可能影响模型复杂度')
      }
      
      if (maxVaryingVectors < 8) {
        result.warnings.push('varying变量数量受限，可能影响着色器复杂度')
      }
      
      console.log('WebGL诊断结果:', {
        版本: result.webglVersion,
        最大纹理尺寸: maxTextureSize,
        最大顶点属性: maxVertexAttribs,
        最大Varying向量: maxVaryingVectors,
        支持的扩展: result.extensions
      })
      
    } catch (error) {
      result.errors.push(`WebGL诊断失败: ${error.message}`)
    }
    
    return result
  }
}

module.exports = GameEngine 